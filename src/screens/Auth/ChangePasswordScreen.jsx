import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card from '../../components/ui/Card';
import './AuthScreen.css';

export default function ChangePasswordScreen() {
  const { changePassword, isLoading, error } = useAuth();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    current_password: '',
    new_password: '',
    new_password_confirm: ''
  });
  const [fieldErrors, setFieldErrors] = useState({});
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.current_password) {
      errors.current_password = 'Current password is required';
    }
    
    if (!formData.new_password) {
      errors.new_password = 'New password is required';
    } else if (formData.new_password.length < 8) {
      errors.new_password = 'Password must be at least 8 characters long';
    }
    
    if (!formData.new_password_confirm) {
      errors.new_password_confirm = 'Please confirm your new password';
    } else if (formData.new_password !== formData.new_password_confirm) {
      errors.new_password_confirm = 'Passwords do not match';
    }
    
    if (formData.current_password === formData.new_password) {
      errors.new_password = 'New password must be different from current password';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await changePassword(formData);
      setSuccess(true);
    } catch (error) {
      console.error('Password change failed:', error);
    }
  };

  if (success) {
    return (
      <div className="auth-screen">
        <div className="auth-screen__content">
          <div className="auth-screen__logo">
            <div className="auth-screen__icon">💰</div>
            <h1 className="auth-screen__title">Spliton</h1>
          </div>

          <Card className="auth-screen__card">
            <div className="auth-form">
              <div className="auth-form__header">
                <h2>Password Changed</h2>
                <p>Your password has been changed successfully.</p>
              </div>

              <div className="auth-form__success">
                <div className="auth-form__success-icon">✅</div>
                <p>Your account is now secured with your new password.</p>
              </div>

              <div className="auth-form__actions">
                <Button
                  variant="primary"
                  size="large"
                  onClick={() => navigate('/profile')}
                  className="auth-form__submit"
                >
                  Back to Profile
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-screen">
      <div className="auth-screen__content">
        <div className="auth-screen__logo">
          <div className="auth-screen__icon">💰</div>
          <h1 className="auth-screen__title">Spliton</h1>
        </div>

        <Card className="auth-screen__card">
          <form onSubmit={handleSubmit} className="auth-form">
            <div className="auth-form__header">
              <h2>Change Password</h2>
              <p>Update your account password</p>
            </div>

            {error && (
              <div className="auth-form__error">
                {error}
              </div>
            )}

            <div className="auth-form__fields">
              <Input
                name="current_password"
                type="password"
                label="Current Password"
                placeholder="Enter your current password"
                value={formData.current_password}
                onChange={handleChange}
                error={fieldErrors.current_password}
                disabled={isLoading}
              />

              <Input
                name="new_password"
                type="password"
                label="New Password"
                placeholder="Enter your new password (min. 8 characters)"
                value={formData.new_password}
                onChange={handleChange}
                error={fieldErrors.new_password}
                disabled={isLoading}
              />

              <Input
                name="new_password_confirm"
                type="password"
                label="Confirm New Password"
                placeholder="Confirm your new password"
                value={formData.new_password_confirm}
                onChange={handleChange}
                error={fieldErrors.new_password_confirm}
                disabled={isLoading}
              />
            </div>

            <div className="auth-form__actions">
              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={isLoading}
                className="auth-form__submit"
              >
                Change Password
              </Button>
            </div>

            <div className="auth-screen__links">
              <button
                type="button"
                className="auth-link auth-link--center"
                onClick={() => navigate('/profile')}
              >
                Cancel
              </button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
