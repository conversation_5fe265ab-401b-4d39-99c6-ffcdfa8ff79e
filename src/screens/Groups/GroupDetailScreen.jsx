import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { groupsAPI, expensesAPI } from '../../services/api';
import Button from '../../components/ui/Button';
import Cell from '../../components/ui/Cell';
import Card from '../../components/ui/Card';
import { formatCurrency } from '../../utils/currency';
import { initBackButton } from '../../utils/telegram';
import './GroupDetailScreen.css';

export default function GroupDetailScreen() {
  const { groupId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [group, setGroup] = useState(null);
  const [expenses, setExpenses] = useState([]);
  const [balances, setBalances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Initialize Telegram back button
    const cleanup = initBackButton(() => {
      navigate('/groups');
    });

    loadGroupData();

    return cleanup;
  }, [groupId, navigate]);

  const loadGroupData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [groupData, expensesData, balancesData] = await Promise.all([
        groupsAPI.getGroup(groupId),
        expensesAPI.getExpenses(groupId),
        groupsAPI.getGroupBalances(groupId)
      ]);

      setGroup(groupData);
      setExpenses(expensesData);
      // Convert balances object to the expected format
      setBalances(balancesData.balances || {});
    } catch (error) {
      console.error('Failed to load group data:', error);
      setError('Failed to load group data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddExpense = () => {
    navigate(`/groups/${groupId}/add-expense`);
  };

  const handleSettleUp = () => {
    navigate(`/groups/${groupId}/settle`);
  };

  const handleExpenseClick = (expenseId) => {
    navigate(`/expenses/${expenseId}`);
  };

  const getUserBalance = () => {
    // balances is now a dictionary with user_id as keys
    return balances[user?.id] || 0;
  };

  if (loading) {
    return (
      <div className="screen">
        <div className="loading">Loading group...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="screen">
        <div className="error">
          <p>{error}</p>
          <Button onClick={loadGroupData}>Try Again</Button>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="screen">
        <div className="error">
          <p>Group not found</p>
          <Button onClick={() => navigate('/groups')}>Back to Groups</Button>
        </div>
      </div>
    );
  }

  const userBalance = getUserBalance();
  const isOwed = userBalance > 0;
  const owes = userBalance < 0;

  return (
    <div className="screen">
      <div className="screen__header">
        <h1 className="screen__title">{group.name}</h1>
      </div>

      <div className="screen__content">
        <div className="container">
          {/* Group Info Card */}
          <Card className="group-detail__info-card">
            <div className="group-detail__info">
              <div className="group-detail__avatar">
                {group.name[0]}
              </div>
              <div className="group-detail__meta">
                <div className="group-detail__name">{group.name}</div>
                {group.description && (
                  <div className="group-detail__description text-hint">
                    {group.description}
                  </div>
                )}
                <div className="group-detail__members text-hint">
                  {group.member_count} members
                </div>
              </div>
            </div>
          </Card>

          {/* Balance Card */}
          <Card className="group-detail__balance-card">
            <div className="group-detail__balance">
              <div className="group-detail__balance-label">Your balance</div>
              <div className={`group-detail__balance-amount ${
                isOwed ? 'group-detail__balance-amount--positive' :
                owes ? 'group-detail__balance-amount--negative' :
                'group-detail__balance-amount--neutral'
              }`}>
                {userBalance === 0 ? 'Settled up' : formatCurrency(Math.abs(userBalance))}
              </div>
              {userBalance !== 0 && (
                <div className="group-detail__balance-status text-hint">
                  {isOwed ? 'You are owed' : 'You owe'}
                </div>
              )}
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="group-detail__actions">
            <Button
              variant="primary"
              size="large"
              onClick={handleAddExpense}
              className="tg-button--full-width"
            >
              + Add Expense
            </Button>
            {userBalance !== 0 && (
              <Button
                variant="secondary"
                size="large"
                onClick={handleSettleUp}
                className="tg-button--full-width"
              >
                Settle Up
              </Button>
            )}
          </div>

          {/* Recent Expenses */}
          {expenses.length > 0 ? (
            <div className="section">
              <div className="section__header">Recent Expenses</div>
              <div className="section__content">
                {expenses.slice(0, 10).map((expense) => (
                  <Cell
                    key={expense.id}
                    title={expense.description}
                    subtitle={`Created by ${expense.created_by_username} • ${new Date(expense.created_at).toLocaleDateString()}`}
                    before={
                      <div className="expense-icon">
                        💰
                      </div>
                    }
                    after={
                      <div className="expense-amount">
                        {formatCurrency(expense.amount)}
                      </div>
                    }
                    onClick={() => handleExpenseClick(expense.id)}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-state__icon">💸</div>
              <div className="empty-state__title">No Expenses Yet</div>
              <div className="empty-state__description">
                Add your first expense to start tracking group spending.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
