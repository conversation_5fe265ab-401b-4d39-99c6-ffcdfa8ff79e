.tg-cell {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--tg-section-bg-color);
  border-bottom: 1px solid var(--tg-secondary-bg-color);
  transition: background-color 0.2s ease;
  position: relative;
  min-height: 44px;
}

.tg-cell:last-child {
  border-bottom: none;
}

.tg-cell--clickable {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.tg-cell--clickable:hover:not(.tg-cell--disabled) {
  background-color: var(--tg-secondary-bg-color);
}

.tg-cell--clickable:active:not(.tg-cell--disabled) {
  opacity: 0.7;
}

.tg-cell--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tg-cell__before {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tg-cell__content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tg-cell__after {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--tg-hint-color);
}

.tg-cell__title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--tg-text-color);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tg-cell__subtitle {
  font-size: var(--font-size-sm);
  color: var(--tg-subtitle-text-color);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tg-cell__description {
  font-size: var(--font-size-sm);
  color: var(--tg-hint-color);
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.tg-cell__children {
  margin-top: var(--spacing-xs);
}

/* Avatar variant */
.tg-cell__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--tg-secondary-bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-lg);
  color: var(--tg-text-color);
  overflow: hidden;
}

.tg-cell__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Icon variant */
.tg-cell__icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--tg-hint-color);
}

/* Badge variant */
.tg-cell__badge {
  background-color: var(--tg-destructive-text-color);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Chevron variant */
.tg-cell__chevron {
  width: 8px;
  height: 8px;
  border-right: 2px solid var(--tg-hint-color);
  border-bottom: 2px solid var(--tg-hint-color);
  transform: rotate(-45deg);
  margin-left: var(--spacing-sm);
}

/* Switch variant */
.tg-cell__switch {
  width: 44px;
  height: 24px;
  background-color: var(--tg-secondary-bg-color);
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tg-cell__switch--active {
  background-color: var(--tg-button-color);
}

.tg-cell__switch::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.tg-cell__switch--active::after {
  transform: translateX(20px);
}
