import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { groupsAPI } from '../../services/api';
import Button from '../../components/ui/Button';
import Cell from '../../components/ui/Cell';
import Card from '../../components/ui/Card';
import CreateGroupModal from '../../components/modals/CreateGroupModal';
import { formatCurrency } from '../../utils/currency';
import './GroupListScreen.css';

export default function GroupListScreen() {
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      setLoading(true);
      const groupsData = await groupsAPI.getGroups();
      setGroups(groupsData);
    } catch (error) {
      console.error('Failed to load groups:', error);
      console.error('Error details:', error.response?.data || error.message);
      setError('Failed to load groups');
    } finally {
      setLoading(false);
    }
  };

  const handleGroupClick = (groupId) => {
    navigate(`/groups/${groupId}`);
  };

  const handleCreateGroup = () => {
    setShowCreateModal(true);
  };

  const handleCreateGroupSubmit = async (groupData) => {
    try {
      const newGroup = await groupsAPI.createGroup(groupData);
      setGroups(prevGroups => [newGroup, ...prevGroups]);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Failed to create group:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleProfileClick = () => {
    navigate('/profile');
  };

  if (loading) {
    return (
      <div className="screen">
        <div className="loading">Loading groups...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="screen">
        <div className="error">
          <p>{error}</p>
          <Button onClick={loadGroups}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="screen">
      <div className="screen__header">
        <div className="group-list__header">
          <h1 className="screen__title">Groups</h1>
          <Button
            variant="ghost"
            size="small"
            onClick={handleLogout}
          >
            Logout
          </Button>
        </div>
      </div>

      <div className="screen__content">
        <div className="container">
          {/* User info card */}
          <Card className="group-list__user-card group-list__user-card--clickable" onClick={handleProfileClick}>
            <div className="group-list__user-info">
              <div className="tg-cell__avatar">
                {user?.first_name?.[0] || user?.username?.[0] || '?'}
              </div>
              <div>
                <div className="group-list__user-name">
                  {user?.first_name} {user?.last_name}
                </div>
                <div className="group-list__user-username text-hint">
                  @{user?.username}
                </div>
              </div>
            </div>
          </Card>

          {/* Create group button */}
          <Button
            variant="primary"
            size="large"
            onClick={handleCreateGroup}
            className="tg-button--full-width group-list__create-button"
          >
            + Create New Group
          </Button>

          {/* Groups list */}
          {groups.length > 0 ? (
            <div className="section">
              <div className="section__header">Your Groups</div>
              <div className="section__content">
                {groups.map((group) => (
                  <Cell
                    key={group.id}
                    title={group.name}
                    subtitle={`${group.member_count} members`}
                    before={
                      <div className="tg-cell__avatar">
                        {group.name[0]}
                      </div>
                    }
                    after={<div className="tg-cell__chevron" />}
                    onClick={() => handleGroupClick(group.id)}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-state__icon">👥</div>
              <div className="empty-state__title">No Groups Yet</div>
              <div className="empty-state__description">
                Create your first group to start splitting expenses with friends.
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Group Modal */}
      <CreateGroupModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateGroupSubmit}
      />
    </div>
  );
}
