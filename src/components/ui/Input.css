.tg-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.tg-input__label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--tg-text-color);
}

.tg-input {
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--tg-section-bg-color);
  border: 1px solid var(--tg-secondary-bg-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  color: var(--tg-text-color);
  transition: all 0.2s ease;
}

.tg-input::placeholder {
  color: var(--tg-hint-color);
}

.tg-input:focus {
  border-color: var(--tg-button-color);
  box-shadow: 0 0 0 2px rgba(36, 129, 204, 0.1);
}

.tg-input--error {
  border-color: var(--tg-destructive-text-color);
}

.tg-input--error:focus {
  border-color: var(--tg-destructive-text-color);
  box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.1);
}

.tg-input--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tg-input__error {
  font-size: var(--font-size-sm);
  color: var(--tg-destructive-text-color);
  margin-top: var(--spacing-xs);
}

/* Currency input variant */
.tg-input--currency {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* Number input variant */
.tg-input--number {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* Search input variant */
.tg-input--search {
  padding-left: 40px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='m21 21-4.35-4.35'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: 12px center;
  background-size: 16px;
}

/* Multiline/Textarea variant */
.tg-input--multiline {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.4;
}

/* Legacy textarea class for backward compatibility */
.tg-textarea {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.4;
}

/* Input group */
.tg-input-group {
  display: flex;
  gap: var(--spacing-sm);
}

.tg-input-group .tg-input {
  flex: 1;
}

/* Input with addon */
.tg-input-addon {
  display: flex;
  align-items: center;
  background-color: var(--tg-secondary-bg-color);
  border: 1px solid var(--tg-secondary-bg-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.tg-input-addon .tg-input {
  border: none;
  border-radius: 0;
  background: transparent;
}

.tg-input-addon .tg-input:focus {
  box-shadow: none;
}

.tg-input-addon__text {
  padding: var(--spacing-md);
  color: var(--tg-hint-color);
  font-size: var(--font-size-md);
  white-space: nowrap;
}
