"""
Tests for expenses serializers.
"""
from django.test import TestCase
from apps.accounts.models import User
from apps.expenses.models import Group, Membership, Expense, ExpenseParticipant, Settlement
from apps.expenses.serializers import (
    GroupSerializer,
    MembershipSerializer,
    ExpenseParticipantSerializer,
    ExpenseSerializer,
    SettlementSerializer
)


class GroupSerializerTest(TestCase):
    """Test cases for GroupSerializer."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )

    def test_group_serialization_with_uuid(self):
        """Test Group serialization includes UUID fields."""
        serializer = GroupSerializer(self.group)
        data = serializer.data
        
        # Check UUID fields are serialized as strings
        self.assertIsInstance(data['id'], str)
        self.assertIsInstance(data['created_by'], str)
        self.assertEqual(data['id'], str(self.group.id))
        self.assertEqual(data['created_by'], str(self.user.id))
        
        # Check other fields
        self.assertEqual(data['name'], 'Test Group')
        self.assertTrue(data['is_published'])
        self.assertFalse(data['is_deleted'])

    def test_group_deserialization_with_uuid(self):
        """Test Group deserialization handles UUID strings."""
        data = {
            'name': 'New Group',
            'created_by': str(self.user.id),
            'telegram_chat_id': 123456789
        }
        
        serializer = GroupSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        group = serializer.save()
        self.assertEqual(group.name, 'New Group')
        self.assertEqual(group.created_by, self.user)
        self.assertEqual(group.telegram_chat_id, 123456789)


class MembershipSerializerTest(TestCase):
    """Test cases for MembershipSerializer."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        self.membership = Membership.objects.create(
            user=self.user2,
            group=self.group,
            role='member'
        )

    def test_membership_serialization_with_uuid(self):
        """Test Membership serialization includes UUID and user info."""
        serializer = MembershipSerializer(self.membership)
        data = serializer.data
        
        # Check UUID fields
        self.assertIsInstance(data['id'], str)
        self.assertIsInstance(data['user_id'], str)
        self.assertIsInstance(data['group_id'], str)
        
        # Check user information is included
        self.assertEqual(data['username'], 'user2')
        self.assertEqual(data['user_id'], str(self.user2.id))
        self.assertEqual(data['group_id'], str(self.group.id))
        self.assertEqual(data['role'], 'member')

    def test_membership_deserialization_with_uuid(self):
        """Test Membership deserialization handles UUID strings."""
        user3 = User.objects.create_user(
            username='user3',
            email='<EMAIL>',
            password='testpass123'
        )
        
        data = {
            'user': str(user3.id),
            'group': str(self.group.id),
            'role': 'admin'
        }
        
        serializer = MembershipSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        membership = serializer.save()
        self.assertEqual(membership.user, user3)
        self.assertEqual(membership.group, self.group)
        self.assertEqual(membership.role, 'admin')


class ExpenseParticipantSerializerTest(TestCase):
    """Test cases for ExpenseParticipantSerializer."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )
        self.expense = Expense.objects.create(
            group=self.group,
            created_by=self.user,
            description='Test Expense',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        self.participant = ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user,
            share=1000,
            paid=2000
        )

    def test_expense_participant_serialization_currency_neutral(self):
        """Test ExpenseParticipant serialization uses currency-neutral fields."""
        serializer = ExpenseParticipantSerializer(self.participant)
        data = serializer.data
        
        # Check UUID fields
        self.assertIsInstance(data['id'], str)  # ExpenseParticipant UUID
        self.assertIsInstance(data['user_id'], str)  # User UUID
        self.assertEqual(data['id'], str(self.participant.id))
        self.assertEqual(data['user_id'], str(self.user.id))
        
        # Check currency-neutral field names
        self.assertEqual(data['share'], 1000)  # Not 'share_cents'
        self.assertEqual(data['paid'], 2000)   # Not 'paid_cents'
        
        # Check major units conversion
        self.assertEqual(data['share_major_units'], '10.00')
        self.assertEqual(data['paid_major_units'], '20.00')
        self.assertEqual(data['net_major_units'], '10.00')  # paid - share
        
        # Check user information
        self.assertEqual(data['username'], 'testuser')

    def test_expense_participant_field_distinction(self):
        """Test that id and user_id fields serve different purposes."""
        serializer = ExpenseParticipantSerializer(self.participant)
        data = serializer.data
        
        # Both fields should exist but be different
        self.assertIn('id', data)
        self.assertIn('user_id', data)
        self.assertNotEqual(data['id'], data['user_id'])
        
        # id should be ExpenseParticipant's UUID
        self.assertEqual(data['id'], str(self.participant.id))
        
        # user_id should be User's UUID
        self.assertEqual(data['user_id'], str(self.user.id))

    def test_expense_participant_deserialization(self):
        """Test ExpenseParticipant deserialization with currency-neutral fields."""
        data = {
            'user_id': str(self.user.id),
            'share': 1500,  # Using 'share' not 'share_cents'
            'paid': 1000    # Using 'paid' not 'paid_cents'
        }
        
        serializer = ExpenseParticipantSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        # Note: We can't save without expense context in this test
        # but we can verify the data is valid


class ExpenseSerializerTest(TestCase):
    """Test cases for ExpenseSerializer."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        self.expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount=5000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        # Add participants
        ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user1,
            share=2500,
            paid=5000
        )
        ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user2,
            share=2500,
            paid=0
        )

    def test_expense_serialization_with_participants(self):
        """Test Expense serialization includes participants with UUID fields."""
        serializer = ExpenseSerializer(self.expense)
        data = serializer.data
        
        # Check expense UUID fields
        self.assertIsInstance(data['id'], str)
        self.assertIsInstance(data['group'], str)
        self.assertIsInstance(data['created_by'], str)
        
        # Check currency-neutral field names
        self.assertEqual(data['amount'], 5000)  # Not 'amount_cents'
        self.assertEqual(data['amount_major_units'], '50.00')
        
        # Check participants are included
        self.assertIn('participants', data)
        self.assertEqual(len(data['participants']), 2)
        
        # Check participant structure
        participant = data['participants'][0]
        self.assertIn('id', participant)  # ExpenseParticipant UUID
        self.assertIn('user_id', participant)  # User UUID
        self.assertIn('username', participant)
        self.assertIn('share', participant)
        self.assertIn('paid', participant)

    def test_expense_deserialization_with_participants(self):
        """Test Expense deserialization handles nested participants."""
        data = {
            'group': str(self.group.id),
            'description': 'New Expense',
            'amount': 3000,
            'currency': 'EUR',
            'occurred_at': '2025-09-22T15:00:00Z',
            'participants': [
                {
                    'user_id': str(self.user1.id),
                    'share': 1500,
                    'paid': 3000
                },
                {
                    'user_id': str(self.user2.id),
                    'share': 1500,
                    'paid': 0
                }
            ]
        }
        
        serializer = ExpenseSerializer(data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        
        # Test that validation passes with UUID strings
        self.assertEqual(serializer.validated_data['amount'], 3000)
        self.assertEqual(len(serializer.validated_data['participants']), 2)


class SettlementSerializerTest(TestCase):
    """Test cases for SettlementSerializer."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        self.settlement = Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount=2500,
            currency='USD',
            settled_at='2025-09-22T14:00:00Z',
            note='Cash payment'
        )

    def test_settlement_serialization_with_uuid(self):
        """Test Settlement serialization includes UUID fields and user info."""
        serializer = SettlementSerializer(self.settlement)
        data = serializer.data
        
        # Check UUID fields
        self.assertIsInstance(data['id'], str)
        self.assertIsInstance(data['group'], str)
        self.assertIsInstance(data['payer_id'], str)
        self.assertIsInstance(data['payee_id'], str)
        
        # Check currency-neutral field names
        self.assertEqual(data['amount'], 2500)  # Not 'amount_cents'
        self.assertEqual(data['amount_major_units'], '25.00')
        
        # Check user information is included
        self.assertEqual(data['payer_username'], 'user2')
        self.assertEqual(data['payee_username'], 'user1')
        self.assertEqual(data['note'], 'Cash payment')

    def test_settlement_deserialization_with_uuid(self):
        """Test Settlement deserialization handles UUID strings."""
        data = {
            'group': str(self.group.id),
            'payer': str(self.user1.id),
            'payee': str(self.user2.id),
            'amount': 1500,
            'currency': 'EUR',
            'settled_at': '2025-09-22T16:00:00Z',
            'note': 'Bank transfer'
        }
        
        serializer = SettlementSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        settlement = serializer.save()
        self.assertEqual(settlement.amount, 1500)
        self.assertEqual(settlement.currency, 'EUR')
        self.assertEqual(settlement.note, 'Bank transfer')
