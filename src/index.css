/* Telegram Mini App Theme */
:root {
  /* Light theme colors */
  --tg-bg-color: #ffffff;
  --tg-text-color: #000000;
  --tg-hint-color: #999999;
  --tg-link-color: #2481cc;
  --tg-button-color: #2481cc;
  --tg-button-text-color: #ffffff;
  --tg-secondary-bg-color: #f1f1f1;
  --tg-section-bg-color: #ffffff;
  --tg-section-header-text-color: #6d6d71;
  --tg-subtitle-text-color: #999999;
  --tg-destructive-text-color: #ff3b30;

  /* Theme-specific aliases */
  --tg-theme-bg-color: var(--tg-bg-color);
  --tg-theme-text-color: var(--tg-text-color);
  --tg-theme-hint-color: var(--tg-hint-color);
  --tg-theme-link-color: var(--tg-link-color);
  --tg-theme-button-color: var(--tg-button-color);
  --tg-theme-button-text-color: var(--tg-button-text-color);
  --tg-theme-secondary-bg-color: var(--tg-secondary-bg-color);
  --tg-theme-section-bg-color: var(--tg-section-bg-color);
  --tg-theme-separator-color: #e5e5ea;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Telegram spacing variables */
  --tg-spacing-4: 4px;
  --tg-spacing-8: 8px;
  --tg-spacing-12: 12px;
  --tg-spacing-16: 16px;
  --tg-spacing-20: 20px;
  --tg-spacing-24: 24px;
  --tg-spacing-32: 32px;

  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 16px;

  /* Telegram border radius variables */
  --tg-border-radius-4: 4px;
  --tg-border-radius-8: 8px;
  --tg-border-radius-12: 12px;
  --tg-border-radius-16: 16px;

  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;

  /* Telegram typography variables */
  --tg-font-size-12: 12px;
  --tg-font-size-14: 14px;
  --tg-font-size-16: 16px;
  --tg-font-size-18: 18px;
  --tg-font-size-20: 20px;
  --tg-font-weight-normal: 400;
  --tg-font-weight-medium: 500;
  --tg-font-weight-semibold: 600;
  --tg-font-weight-bold: 700;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Dark theme */
.dark-theme {
  --tg-bg-color: #212121;
  --tg-text-color: #ffffff;
  --tg-hint-color: #708499;
  --tg-link-color: #6ab7ff;
  --tg-button-color: #5288c1;
  --tg-button-text-color: #ffffff;
  --tg-secondary-bg-color: #181818;
  --tg-section-bg-color: #1c1c1d;
  --tg-section-header-text-color: #8e8e93;
  --tg-subtitle-text-color: #8e8e93;
  --tg-destructive-text-color: #ff453a;

  /* Theme-specific aliases for dark mode */
  --tg-theme-bg-color: var(--tg-bg-color);
  --tg-theme-text-color: var(--tg-text-color);
  --tg-theme-hint-color: var(--tg-hint-color);
  --tg-theme-link-color: var(--tg-link-color);
  --tg-theme-button-color: var(--tg-button-color);
  --tg-theme-button-text-color: var(--tg-button-text-color);
  --tg-theme-secondary-bg-color: var(--tg-secondary-bg-color);
  --tg-theme-section-bg-color: var(--tg-section-bg-color);
  --tg-theme-separator-color: #3a3a3c;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--tg-text-color);
  background-color: var(--tg-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input, textarea, select {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
}

input:focus, textarea:focus, select:focus {
  outline: none;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Remove default link styles */
a {
  color: var(--tg-link-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Utility classes */
.text-hint {
  color: var(--tg-hint-color);
}

.text-subtitle {
  color: var(--tg-subtitle-text-color);
}

.text-destructive {
  color: var(--tg-destructive-text-color);
}

.bg-secondary {
  background-color: var(--tg-secondary-bg-color);
}

.bg-section {
  background-color: var(--tg-section-bg-color);
}
