.loading-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--tg-bg-color);
}

.loading-screen__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.loading-screen__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--tg-secondary-bg-color);
  border-top: 3px solid var(--tg-button-color);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

.loading-screen__text {
  font-size: var(--font-size-lg);
  color: var(--tg-hint-color);
  font-weight: 500;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
