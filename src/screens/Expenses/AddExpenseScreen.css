/* Add Expense Screen Styles */

.add-expense__basic-info {
  margin-bottom: var(--tg-spacing-16);
}

.form-field {
  margin-bottom: var(--tg-spacing-16);
}

.form-field:last-child {
  margin-bottom: 0;
}

/* Split Options */
.add-expense__split-options {
  margin-bottom: var(--tg-spacing-16);
}

.split-type-selector {
  display: flex;
  gap: var(--tg-spacing-8);
}

.split-type-selector .tg-button {
  flex: 1;
}

/* Participant Split */
.participant-split {
  min-width: 80px;
  text-align: right;
  font-size: var(--tg-font-size-14);
  font-weight: var(--tg-font-weight-medium);
  color: var(--tg-theme-text-color);
}

.participant-split .tg-input {
  width: 80px;
  padding: var(--tg-spacing-4) var(--tg-spacing-8);
  font-size: var(--tg-font-size-14);
  text-align: right;
}

/* Error Message */
.error-message {
  background-color: var(--tg-color-red-light);
  color: var(--tg-color-red);
  padding: var(--tg-spacing-12);
  border-radius: var(--tg-border-radius-8);
  margin-bottom: var(--tg-spacing-16);
  font-size: var(--tg-font-size-14);
}

/* Radio and Checkbox Styling */
input[type="radio"],
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: var(--tg-theme-button-color);
}

/* Section Spacing */
.section {
  margin-bottom: var(--tg-spacing-16);
}

/* Responsive Design */
@media (max-width: 480px) {
  .split-type-selector {
    flex-direction: column;
  }
  
  .participant-split .tg-input {
    width: 70px;
  }
}
