{"name": "spliton-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.12.2", "date-fns": "^4.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-router-dom": "^6.30.1"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "vite": "^5.4.20"}}