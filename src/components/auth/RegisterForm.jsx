import { useState } from 'react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import './AuthForms.css';

export default function RegisterForm({ onSubmit, loading, error }) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    password_confirm: '',
    first_name: '',
    last_name: ''
  });
  const [fieldErrors, setFieldErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters long';
    }
    
    if (!formData.password_confirm) {
      errors.password_confirm = 'Please confirm your password';
    } else if (formData.password !== formData.password_confirm) {
      errors.password_confirm = 'Passwords do not match';
    }
    
    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="auth-form">
      <div className="auth-form__header">
        <h2>Create Account</h2>
        <p>Sign up to start splitting expenses with friends</p>
      </div>

      {error && (
        <div className="auth-form__error">
          {error}
        </div>
      )}

      <div className="auth-form__fields">
        <div className="auth-form__name-fields">
          <Input
            name="first_name"
            type="text"
            label="First Name"
            placeholder="Enter your first name"
            value={formData.first_name}
            onChange={handleChange}
            error={fieldErrors.first_name}
            disabled={loading}
          />

          <Input
            name="last_name"
            type="text"
            label="Last Name"
            placeholder="Enter your last name"
            value={formData.last_name}
            onChange={handleChange}
            error={fieldErrors.last_name}
            disabled={loading}
          />
        </div>

        <Input
          name="email"
          type="email"
          label="Email"
          placeholder="Enter your email"
          value={formData.email}
          onChange={handleChange}
          error={fieldErrors.email}
          disabled={loading}
        />

        <Input
          name="password"
          type="password"
          label="Password"
          placeholder="Create a password (min. 8 characters)"
          value={formData.password}
          onChange={handleChange}
          error={fieldErrors.password}
          disabled={loading}
        />

        <Input
          name="password_confirm"
          type="password"
          label="Confirm Password"
          placeholder="Confirm your password"
          value={formData.password_confirm}
          onChange={handleChange}
          error={fieldErrors.password_confirm}
          disabled={loading}
        />
      </div>

      <div className="auth-form__actions">
        <Button
          type="submit"
          variant="primary"
          size="large"
          loading={loading}
          className="auth-form__submit"
        >
          Create Account
        </Button>
      </div>
    </form>
  );
}
