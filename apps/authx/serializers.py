"""
Serializers for authentication endpoints.
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from apps.accounts.models import User, AuthenticationProvider, PasswordResetToken


class TelegramWebAppVerifySerializer(serializers.Serializer):
    """
    Serializer for Telegram WebApp verification request.
    """
    initData = serializers.CharField(
        help_text="The initData string from Telegram WebApp"
    )



class AuthenticationProviderSerializer(serializers.ModelSerializer):
    """
    Serializer for AuthenticationProvider model.
    """
    class Meta:
        model = AuthenticationProvider
        fields = [
            'id', 'provider', 'provider_username', 'provider_email',
            'is_primary', 'is_verified', 'linked_at', 'last_used_at'
        ]
        read_only_fields = ['id', 'linked_at', 'last_used_at']


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model with authentication providers.
    """
    auth_providers = AuthenticationProviderSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'first_name', 'last_name', 'email', 'profile_photo',
            'date_joined', 'last_login', 'created_at', 'updated_at', 'auth_providers'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login', 'created_at', 'updated_at']


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profile information.
    Allows partial updates of username, first_name, last_name, and profile_photo.
    """

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'profile_photo']

    def validate_username(self, value):
        """Ensure username is unique."""
        user = self.instance
        if User.objects.filter(username=value).exclude(id=user.id).exists():
            raise serializers.ValidationError("A user with this username already exists.")
        return value


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration with email and password.
    """
    password = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'},
        help_text="User's password"
    )

    class Meta:
        model = User
        fields = [
            'email', 'password', 'first_name', 'last_name'
        ]
        extra_kwargs = {
            'email': {'required': True},
            'first_name': {'required': False},
            'last_name': {'required': False},
        }

    def validate_email(self, value):
        """Validate that email is unique."""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_password(self, value):
        """Validate password using Django's password validators."""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return value

    def create(self, validated_data):
        """Create user with email/password authentication provider."""

        # Generate username from email
        email = validated_data['email']
        base_username = email.split('@')[0]

        # Ensure username is unique
        counter = 1
        username = base_username
        while User.objects.filter(username=username).exists():
            username = f"{base_username}_{counter}"
            counter += 1

        # Create user
        user = User.objects.create_user(
            username=username,
            email=validated_data['email'],
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', ''),
        )

        # Create email authentication provider
        AuthenticationProvider.objects.create(
            user=user,
            provider='email',
            provider_user_id=user.email,
            provider_email=user.email,
            is_primary=True,
            is_verified=False,  # Will be verified via email confirmation
        )

        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login with email and password.
    """
    email = serializers.EmailField(
        help_text="User's email address"
    )
    password = serializers.CharField(
        style={'input_type': 'password'},
        help_text="User's password"
    )

    def validate(self, attrs):
        """Validate user credentials."""
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            # Try to find user by email
            try:
                user = User.objects.get(email=email)
                username = user.username
            except User.DoesNotExist:
                raise serializers.ValidationError({
                    'email': 'No user found with this email address.'
                })

            # Authenticate with username (Django's default)
            user = authenticate(username=username, password=password)

            if not user:
                raise serializers.ValidationError({
                    'password': 'Invalid password.'
                })

            if not user.is_active:
                raise serializers.ValidationError({
                    'email': 'User account is disabled.'
                })

            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError({
                'email': 'Email is required.',
                'password': 'Password is required.'
            })


class LinkAccountSerializer(serializers.Serializer):
    """
    Serializer for linking an authentication provider to an existing account.
    """
    provider = serializers.ChoiceField(
        choices=AuthenticationProvider.PROVIDER_CHOICES,
        help_text="The authentication provider to link"
    )
    provider_user_id = serializers.CharField(
        max_length=255,
        help_text="User ID from the external provider"
    )
    provider_username = serializers.CharField(
        max_length=255,
        required=False,
        allow_blank=True,
        help_text="Username from the external provider"
    )
    provider_email = serializers.EmailField(
        required=False,
        allow_blank=True,
        help_text="Email from the external provider"
    )
    provider_data = serializers.JSONField(
        required=False,
        default=dict,
        help_text="Additional provider-specific data"
    )

    def validate(self, attrs):
        """Validate that the provider isn't already linked to another account."""
        provider = attrs['provider']
        provider_user_id = attrs['provider_user_id']

        # Check if this provider is already linked to any account
        existing_provider = AuthenticationProvider.objects.filter(
            provider=provider,
            provider_user_id=provider_user_id
        ).first()

        if existing_provider:
            raise serializers.ValidationError({
                'provider': f'This {provider} account is already linked to another user.'
            })

        return attrs


class UnlinkAccountSerializer(serializers.Serializer):
    """
    Serializer for unlinking an authentication provider from an account.
    """
    provider_id = serializers.UUIDField(
        help_text="UUID of the authentication provider to unlink"
    )

    def validate_provider_id(self, value):
        """Validate that the provider exists and belongs to the current user."""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("Authentication required.")

        try:
            provider = AuthenticationProvider.objects.get(
                id=value,
                user=request.user
            )
        except AuthenticationProvider.DoesNotExist:
            raise serializers.ValidationError("Authentication provider not found.")

        # Don't allow unlinking the last authentication method
        user_providers_count = AuthenticationProvider.objects.filter(
            user=request.user
        ).count()

        if user_providers_count <= 1:
            raise serializers.ValidationError(
                "Cannot unlink the last authentication method. "
                "Please add another authentication method first."
            )

        return value


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request.
    """
    email = serializers.EmailField(
        help_text="Email address of the user requesting password reset"
    )

    def validate_email(self, value):
        """Validate that a user with this email exists."""
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            # Don't reveal whether the email exists or not for security
            pass
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation.
    """
    email = serializers.EmailField(
        help_text="Email address of the user"
    )
    token = serializers.CharField(
        max_length=100,
        help_text="Password reset token"
    )
    new_password = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'},
        help_text="New password"
    )

    def validate_new_password(self, value):
        """Validate new password using Django's password validators."""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for changing password (authenticated users).
    """
    current_password = serializers.CharField(
        style={'input_type': 'password'},
        help_text="Current password"
    )
    new_password = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'},
        help_text="New password"
    )

    def validate_current_password(self, value):
        """Validate current password."""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("Authentication required.")

        if not request.user.check_password(value):
            raise serializers.ValidationError("Current password is incorrect.")

        return value

    def validate_new_password(self, value):
        """Validate new password using Django's password validators."""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return value


