from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from apps.accounts.models import User
from .models import Group, Membership, Expense, ExpenseParticipant, Settlement
from .services import group_net_balances, simplify_transfers, get_group_summary


class BalanceCalculationTestCase(TestCase):
    """Test balance calculation services."""

    def setUp(self):
        # Create users
        self.user1 = User.objects.create_user(username='user1', first_name='User', last_name='One')
        self.user2 = User.objects.create_user(username='user2', first_name='User', last_name='Two')
        self.user3 = User.objects.create_user(username='user3', first_name='User', last_name='Three')

        # Create group
        self.group = Group.objects.create(name='Test Group', created_by=self.user1)

        # Add members
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')
        Membership.objects.create(user=self.user3, group=self.group, role='member')

    def test_group_net_balances_no_expenses(self):
        """Test balance calculation with no expenses."""
        balances = group_net_balances(self.group)

        expected = {
            self.user1.id: 0,
            self.user2.id: 0,
            self.user3.id: 0,
        }

        self.assertEqual(balances, expected)

    def test_group_net_balances_simple_expense(self):
        """Test balance calculation with simple expense."""
        # Create expense: User1 pays $30, split equally among all 3 users
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Dinner',
            amount_cents=3000,  # $30.00
            currency='USD',
            occurred_at='2023-01-01T12:00:00Z'
        )

        # User1 paid $30, owes $10
        ExpenseParticipant.objects.create(expense=expense, user=self.user1, paid_cents=3000, share_cents=1000)
        # User2 paid $0, owes $10
        ExpenseParticipant.objects.create(expense=expense, user=self.user2, paid_cents=0, share_cents=1000)
        # User3 paid $0, owes $10
        ExpenseParticipant.objects.create(expense=expense, user=self.user3, paid_cents=0, share_cents=1000)

        balances = group_net_balances(self.group)

        expected = {
            self.user1.id: 2000,  # Paid $30, owes $10, net +$20
            self.user2.id: -1000,  # Paid $0, owes $10, net -$10
            self.user3.id: -1000,  # Paid $0, owes $10, net -$10
        }

        self.assertEqual(balances, expected)

    def test_group_net_balances_with_settlements(self):
        """Test balance calculation with settlements."""
        # Create expense: User1 pays $30, split equally
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Dinner',
            amount_cents=3000,
            currency='USD',
            occurred_at='2023-01-01T12:00:00Z'
        )

        ExpenseParticipant.objects.create(expense=expense, user=self.user1, paid_cents=3000, share_cents=1000)
        ExpenseParticipant.objects.create(expense=expense, user=self.user2, paid_cents=0, share_cents=1000)
        ExpenseParticipant.objects.create(expense=expense, user=self.user3, paid_cents=0, share_cents=1000)

        # User2 pays User1 $10
        Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount_cents=1000,
            currency='USD',
            settled_at='2023-01-02T12:00:00Z'
        )

        balances = group_net_balances(self.group)

        expected = {
            self.user1.id: 1000,  # Was +$20, received $10, now +$10
            self.user2.id: 0,     # Was -$10, paid $10, now $0
            self.user3.id: -1000, # Still owes $10
        }

        self.assertEqual(balances, expected)

    def test_simplify_transfers_simple(self):
        """Test transfer simplification with simple case."""
        balances = {
            1: 2000,   # User1 is owed $20
            2: -1000,  # User2 owes $10
            3: -1000,  # User3 owes $10
        }

        transfers = simplify_transfers(balances)

        # Should suggest 2 transfers: User2->User1 $10, User3->User1 $10
        self.assertEqual(len(transfers), 2)

        # Check that transfers balance out
        total_from_2 = sum(amount for from_id, to_id, amount in transfers if from_id == 2)
        total_from_3 = sum(amount for from_id, to_id, amount in transfers if from_id == 3)
        total_to_1 = sum(amount for from_id, to_id, amount in transfers if to_id == 1)

        self.assertEqual(total_from_2, 1000)
        self.assertEqual(total_from_3, 1000)
        self.assertEqual(total_to_1, 2000)

    def test_simplify_transfers_complex(self):
        """Test transfer simplification with complex case."""
        balances = {
            1: 1500,   # User1 is owed $15
            2: 500,    # User2 is owed $5
            3: -1000,  # User3 owes $10
            4: -1000,  # User4 owes $10
        }

        transfers = simplify_transfers(balances)

        # Verify that all balances are settled
        net_balances = {1: 1500, 2: 500, 3: -1000, 4: -1000}

        for from_id, to_id, amount in transfers:
            net_balances[from_id] += amount
            net_balances[to_id] -= amount

        # All balances should be zero after transfers
        for balance in net_balances.values():
            self.assertEqual(balance, 0)

    def test_get_group_summary(self):
        """Test group summary calculation."""
        # Create expense
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount_cents=3000,
            currency='USD',
            occurred_at='2023-01-01T12:00:00Z'
        )

        ExpenseParticipant.objects.create(expense=expense, user=self.user1, paid_cents=3000, share_cents=1000)
        ExpenseParticipant.objects.create(expense=expense, user=self.user2, paid_cents=0, share_cents=1000)
        ExpenseParticipant.objects.create(expense=expense, user=self.user3, paid_cents=0, share_cents=1000)

        summary = get_group_summary(self.group)

        self.assertEqual(summary['group_id'], self.group.id)
        self.assertEqual(summary['group_name'], 'Test Group')
        self.assertEqual(summary['member_count'], 3)
        self.assertEqual(summary['total_expenses_cents'], 3000)
        self.assertEqual(summary['expense_count'], 1)
        self.assertEqual(summary['settlement_count'], 0)
        self.assertEqual(summary['total_owed_cents'], 2000)  # User1 is owed $20
        self.assertEqual(summary['total_owing_cents'], 2000)  # User2+User3 owe $20 total
        self.assertFalse(summary['is_settled'])
        self.assertEqual(len(summary['suggested_transfers']), 2)


class GroupAPITestCase(APITestCase):
    """Test Group API endpoints."""

    def setUp(self):
        self.user1 = User.objects.create_user(username='user1', password='testpass')
        self.user2 = User.objects.create_user(username='user2', password='testpass')

        self.group = Group.objects.create(name='Test Group', created_by=self.user1)
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')

    def test_list_groups_authenticated(self):
        """Test listing groups for authenticated user."""
        self.client.force_authenticate(user=self.user1)

        url = reverse('expenses:group-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Test Group')

    def test_list_groups_unauthenticated(self):
        """Test listing groups fails for unauthenticated user."""
        url = reverse('expenses:group-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_group(self):
        """Test creating a new group."""
        self.client.force_authenticate(user=self.user1)

        url = reverse('expenses:group-list')
        data = {'name': 'New Group'}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Group')
        self.assertEqual(response.data['created_by'], self.user1.id)

        # Check that creator is added as admin member
        new_group = Group.objects.get(id=response.data['id'])
        membership = Membership.objects.get(user=self.user1, group=new_group)
        self.assertEqual(membership.role, 'admin')

    def test_group_balances(self):
        """Test group balances endpoint."""
        self.client.force_authenticate(user=self.user1)

        # Create an expense
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount_cents=2000,
            currency='USD',
            occurred_at='2023-01-01T12:00:00Z'
        )

        ExpenseParticipant.objects.create(expense=expense, user=self.user1, paid_cents=2000, share_cents=1000)
        ExpenseParticipant.objects.create(expense=expense, user=self.user2, paid_cents=0, share_cents=1000)

        url = reverse('expenses:group-balances', kwargs={'pk': self.group.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('balances', response.data)
        self.assertIn('suggested_transfers', response.data)

        balances = response.data['balances']
        self.assertEqual(balances[str(self.user1.id)], 1000)  # User1 is owed $10
        self.assertEqual(balances[str(self.user2.id)], -1000)  # User2 owes $10


class ExpenseAPITestCase(APITestCase):
    """Test Expense API endpoints."""

    def setUp(self):
        self.user1 = User.objects.create_user(username='user1', password='testpass')
        self.user2 = User.objects.create_user(username='user2', password='testpass')

        self.group = Group.objects.create(name='Test Group', created_by=self.user1)
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')

    def test_create_expense(self):
        """Test creating a new expense."""
        self.client.force_authenticate(user=self.user1)

        url = reverse('expenses:expense-list')
        data = {
            'group': self.group.id,
            'description': 'Test Expense',
            'amount_cents': 2000,
            'currency': 'USD',
            'occurred_at': '2023-01-01T12:00:00Z',
            'participants': [
                {'user_id': self.user1.id, 'share_cents': 1000, 'paid_cents': 2000},
                {'user_id': self.user2.id, 'share_cents': 1000, 'paid_cents': 0},
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['description'], 'Test Expense')
        self.assertEqual(response.data['amount_cents'], 2000)
        self.assertEqual(len(response.data['participants']), 2)

    def test_create_expense_invalid_shares(self):
        """Test creating expense with invalid share distribution."""
        self.client.force_authenticate(user=self.user1)

        url = reverse('expenses:expense-list')
        data = {
            'group': self.group.id,
            'description': 'Test Expense',
            'amount_cents': 2000,
            'currency': 'USD',
            'occurred_at': '2023-01-01T12:00:00Z',
            'participants': [
                {'user_id': self.user1.id, 'share_cents': 1500, 'paid_cents': 2000},  # Shares don't add up
                {'user_id': self.user2.id, 'share_cents': 1000, 'paid_cents': 0},
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
