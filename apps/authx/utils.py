"""
Telegram WebApp authentication utilities.
"""
import hashlib
import hmac
import json
from urllib.parse import unquote, parse_qsl
from typing import Dict, Optional, Any
from django.conf import settings
from django.utils import timezone


def verify_telegram_webapp_data(init_data: str) -> Optional[Dict[str, Any]]:
    """
    Verify Telegram WebApp initData using HMAC-SHA256.
    
    Args:
        init_data: The initData string from Telegram WebApp
        
    Returns:
        Dict containing parsed user data if verification succeeds, None otherwise
    """
    if not settings.TELEGRAM_BOT_TOKEN:
        raise ValueError("TELEGRAM_BOT_TOKEN not configured")
    
    try:
        # Parse the init_data
        parsed_data = dict(parse_qsl(init_data))
        
        # Extract hash and remove it from data for verification
        received_hash = parsed_data.pop('hash', None)
        if not received_hash:
            return None
        
        # Create data check string (sorted key=value pairs joined by \n)
        data_check_arr = []
        for key in sorted(parsed_data.keys()):
            data_check_arr.append(f"{key}={parsed_data[key]}")
        data_check_string = '\n'.join(data_check_arr)
        
        # Create secret key from bot token
        secret_key = hmac.new(
            "WebAppData".encode(),
            settings.TELEGRAM_BOT_TOKEN.encode(),
            hashlib.sha256
        ).digest()
        
        # Calculate expected hash
        expected_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Verify hash
        if not hmac.compare_digest(received_hash, expected_hash):
            return None
        
        # Parse user data
        user_data = json.loads(unquote(parsed_data.get('user', '{}')))
        
        # Add auth_date for additional validation if needed
        auth_date = parsed_data.get('auth_date')
        if auth_date:
            user_data['auth_date'] = int(auth_date)
        
        return user_data
        
    except (ValueError, json.JSONDecodeError, KeyError) as e:
        # Log the error in production
        return None


def create_or_update_user_from_telegram(telegram_data: Dict[str, Any]):
    """
    Create or update Django User and AuthenticationProvider from Telegram data.

    Args:
        telegram_data: Verified Telegram user data

    Returns:
        Tuple of (User instance, created boolean)
    """
    from apps.accounts.models import User, AuthenticationProvider

    telegram_user_id = telegram_data['id']
    username = telegram_data.get('username', '')
    first_name = telegram_data.get('first_name', '')
    last_name = telegram_data.get('last_name', '')
    language_code = telegram_data.get('language_code', '')
    photo_url = telegram_data.get('photo_url', '')

    # Try to find existing AuthenticationProvider for Telegram
    try:
        auth_provider = AuthenticationProvider.objects.get(
            provider='telegram',
            provider_user_id=str(telegram_user_id)
        )
        user = auth_provider.user
        created = False

        # Update provider data
        auth_provider.provider_username = username
        auth_provider.provider_data.update({
            'first_name': first_name,
            'last_name': last_name,
            'language_code': language_code,
            'photo_url': photo_url,
        })
        auth_provider.last_used_at = timezone.now()
        auth_provider.save()

        # Update user profile if not already filled
        if not user.first_name and first_name:
            user.first_name = first_name
        if not user.last_name and last_name:
            user.last_name = last_name
        if not user.profile_photo and photo_url:
            user.profile_photo = photo_url
        user.save()

    except AuthenticationProvider.DoesNotExist:
        # Create new user and AuthenticationProvider
        # Generate unique username from Telegram data
        base_username = username or f"telegram_{telegram_user_id}"
        unique_username = base_username
        counter = 1
        while User.objects.filter(username=unique_username).exists():
            unique_username = f"{base_username}_{counter}"
            counter += 1

        # Create user
        user = User.objects.create(
            username=unique_username,
            first_name=first_name,
            last_name=last_name,
            profile_photo=photo_url
        )
        created = True

        # Create AuthenticationProvider
        auth_provider = AuthenticationProvider.objects.create(
            user=user,
            provider='telegram',
            provider_user_id=str(telegram_user_id),
            provider_username=username,
            is_verified=True,
            is_primary=True,  # First auth method is primary
            provider_data={
                'first_name': first_name,
                'last_name': last_name,
                'language_code': language_code,
                'photo_url': photo_url,
            }
        )

    return user, created


def link_telegram_to_user(user, telegram_data: Dict[str, Any]):
    """
    Link Telegram account to an existing user.

    Args:
        user: Existing Django User instance
        telegram_data: Verified Telegram user data

    Returns:
        AuthenticationProvider instance

    Raises:
        ValueError: If Telegram account is already linked to another user
    """
    from apps.accounts.models import AuthenticationProvider

    telegram_user_id = telegram_data['id']
    username = telegram_data.get('username', '')
    first_name = telegram_data.get('first_name', '')
    last_name = telegram_data.get('last_name', '')
    language_code = telegram_data.get('language_code', '')
    photo_url = telegram_data.get('photo_url', '')

    # Check if this Telegram account is already linked
    existing_provider = AuthenticationProvider.objects.filter(
        provider='telegram',
        provider_user_id=str(telegram_user_id)
    ).first()
    if existing_provider:
        raise ValueError("This Telegram account is already linked to another user.")

    # Check if this user already has a Telegram provider
    existing_user_provider = AuthenticationProvider.objects.filter(
        user=user,
        provider='telegram'
    ).first()
    if existing_user_provider:
        raise ValueError("This user already has a Telegram account linked.")

    # Update user profile if not already filled
    if not user.first_name and first_name:
        user.first_name = first_name
    if not user.last_name and last_name:
        user.last_name = last_name
    if not user.profile_photo and photo_url:
        user.profile_photo = photo_url
    user.save()

    # Create AuthenticationProvider
    auth_provider = AuthenticationProvider.objects.create(
        user=user,
        provider='telegram',
        provider_user_id=str(telegram_user_id),
        provider_username=username,
        is_verified=True,
        provider_data={
            'first_name': first_name,
            'last_name': last_name,
            'language_code': language_code,
            'photo_url': photo_url,
        }
    )

    return auth_provider
