import { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/api';
import { initTelegramWebApp } from '../utils/telegram';

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  telegramWebApp: null
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  SET_TELEGRAM_WEBAPP: 'SET_TELEGRAM_WEBAPP'
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
        error: null
      };
    
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null
      };
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      };
    
    case AUTH_ACTIONS.SET_TELEGRAM_WEBAPP:
      return {
        ...state,
        telegramWebApp: action.payload
      };
    
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext();

// Provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize Telegram WebApp (if available)
  useEffect(() => {
    try {
      const tg = initTelegramWebApp();
      dispatch({ type: AUTH_ACTIONS.SET_TELEGRAM_WEBAPP, payload: tg });
    } catch (error) {
      console.warn('Telegram WebApp not available:', error);
      // Continue without Telegram WebApp
    }
  }, []);

  // Check for existing authentication
  useEffect(() => {
    const checkAuth = async () => {
      console.log('AuthContext: Checking authentication...');
      const token = localStorage.getItem('access_token');
      console.log('AuthContext: Token found:', !!token);

      if (!token) {
        console.log('AuthContext: No token, setting loading to false');
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return;
      }

      try {
        // Try to get user profile with existing token
        console.log('AuthContext: Attempting to get profile...');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auth check timeout')), 10000)
        );

        const userData = await Promise.race([
          authAPI.getProfile(),
          timeoutPromise
        ]);

        console.log('AuthContext: Profile loaded:', userData);

        // Handle different response structures
        const user = userData.user || userData;
        console.log('AuthContext: User data:', user);

        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
      } catch (error) {
        console.error('Auth check failed:', error);
        console.error('Error details:', error.response?.data || error.message);
        localStorage.removeItem('access_token');
        console.log('AuthContext: Auth failed, setting loading to false');
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkAuth();
  }, []);

  // Login function - supports both email/password and Telegram
  const login = async (credentials = null) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      let response;

      if (credentials) {
        // Email/password login
        response = await authAPI.login(credentials);
      } else {
        // Telegram login
        response = await authAPI.verifyTelegramWebApp();
      }

      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.user });
      return response;
    } catch (error) {
      console.error('Login failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Login failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Register function
  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      const response = await authAPI.register(userData);
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.user });
      return response;
    } catch (error) {
      console.error('Registration failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Registration failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Request password reset
  const requestPasswordReset = async (email) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      const response = await authAPI.requestPasswordReset(email);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return response;
    } catch (error) {
      console.error('Password reset request failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Password reset request failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Confirm password reset
  const confirmPasswordReset = async (resetData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      const response = await authAPI.confirmPasswordReset(resetData);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return response;
    } catch (error) {
      console.error('Password reset confirmation failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Password reset failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Change password
  const changePassword = async (passwordData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      const response = await authAPI.changePassword(passwordData);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return response;
    } catch (error) {
      console.error('Password change failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Password change failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Get user profile
  const getProfile = async () => {
    try {
      const response = await authAPI.getProfile();
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.user });
      return response;
    } catch (error) {
      console.error('Get profile failed:', error);
      throw error;
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

    try {
      const response = await authAPI.updateProfile(profileData);
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.user });
      return response;
    } catch (error) {
      console.error('Profile update failed:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Profile update failed';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null });
  };

  const value = {
    ...state,
    login,
    register,
    requestPasswordReset,
    confirmPasswordReset,
    changePassword,
    getProfile,
    updateProfile,
    logout,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
