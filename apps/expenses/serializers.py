"""
Serializers for expenses app.
"""
from rest_framework import serializers
from django.db import transaction
from apps.accounts.models import User
from .models import Group, Membership, Expense, ExpenseParticipant, Settlement


class MembershipSerializer(serializers.ModelSerializer):
    """
    Serializer for Membership model.
    """
    user_id = serializers.UUIDField(source='user.id', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)

    class Meta:
        model = Membership
        fields = ['id', 'user_id', 'username', 'first_name', 'last_name', 'role', 'joined_at']
        read_only_fields = ['id', 'joined_at']


class GroupSerializer(serializers.ModelSerializer):
    """
    Serializer for Group model.
    """
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    members = MembershipSerializer(source='memberships', many=True, read_only=True)
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Group
        fields = [
            'id', 'name', 'created_by', 'created_by_username', 'telegram_chat_id',
            'created_at', 'updated_at', 'members', 'member_count'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def get_member_count(self, obj):
        return obj.memberships.count()
    
    def create(self, validated_data):
        """Create group and add creator as admin member."""
        user = self.context['request'].user
        
        with transaction.atomic():
            group = Group.objects.create(
                created_by=user,
                **validated_data
            )
            
            # Add creator as admin member
            Membership.objects.create(
                user=user,
                group=group,
                role='admin'
            )
            
        return group


class AddMemberSerializer(serializers.Serializer):
    """
    Serializer for adding a member to a group.
    """
    user_id = serializers.UUIDField()
    role = serializers.ChoiceField(choices=Membership.ROLE_CHOICES, default='member')

    def validate_user_id(self, value):
        """Validate that user exists."""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User does not exist.")
        return value

    def validate(self, data):
        """Validate that user is not already a member."""
        group = self.context['group']
        user_id = data['user_id']

        if Membership.objects.filter(group=group, user_id=user_id).exists():
            raise serializers.ValidationError("User is already a member of this group.")

        return data


class ExpenseParticipantSerializer(serializers.ModelSerializer):
    """
    Serializer for ExpenseParticipant model.
    """
    user_id = serializers.UUIDField(source='user.id')
    username = serializers.CharField(source='user.username', read_only=True)
    share_major_units = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    paid_major_units = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    net_major_units = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = ExpenseParticipant
        fields = [
            'id', 'user_id', 'username', 'share', 'paid',
            'share_major_units', 'paid_major_units', 'net_major_units'
        ]
        read_only_fields = ['id']


class ExpenseSerializer(serializers.ModelSerializer):
    """
    Serializer for Expense model.
    """
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    group_name = serializers.CharField(source='group.name', read_only=True)
    participants = ExpenseParticipantSerializer(many=True)
    amount_major_units = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = Expense
        fields = [
            'id', 'group', 'group_name', 'created_by', 'created_by_username',
            'description', 'amount', 'amount_major_units', 'currency',
            'occurred_at', 'created_at', 'updated_at', 'participants'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']

    def validate_participants(self, value):
        """Validate participants data."""
        if not value:
            raise serializers.ValidationError("At least one participant is required.")

        # Check that all participants are unique
        user_ids = [p['user']['id'] for p in value]
        if len(user_ids) != len(set(user_ids)):
            raise serializers.ValidationError("Duplicate participants are not allowed.")

        return value

    def validate(self, data):
        """Validate that share sum equals amount."""
        participants = data.get('participants', [])
        amount = data.get('amount', 0)

        if participants:
            total_share = sum(p.get('share', 0) for p in participants)
            if total_share != amount:
                raise serializers.ValidationError(
                    f"Sum of participant shares ({total_share}) must equal expense amount ({amount})."
                )

        return data

    def create(self, validated_data):
        """Create expense with participants."""
        participants_data = validated_data.pop('participants')
        user = self.context['request'].user

        with transaction.atomic():
            expense = Expense.objects.create(
                created_by=user,
                **validated_data
            )

            # Create participants
            for participant_data in participants_data:
                user_data = participant_data.pop('user')
                ExpenseParticipant.objects.create(
                    expense=expense,
                    user_id=user_data['id'],
                    **participant_data
                )

        return expense


class SettlementSerializer(serializers.ModelSerializer):
    """
    Serializer for Settlement model.
    """
    payer_username = serializers.CharField(source='payer.username', read_only=True)
    payee_username = serializers.CharField(source='payee.username', read_only=True)
    group_name = serializers.CharField(source='group.name', read_only=True)
    amount_major_units = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    # Use separate fields for input to avoid confusion
    payer_id = serializers.UUIDField(write_only=True)
    payee_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = Settlement
        fields = [
            'id', 'group', 'group_name', 'payer', 'payer_id', 'payer_username',
            'payee', 'payee_id', 'payee_username', 'amount', 'amount_major_units',
            'currency', 'note', 'settled_at', 'created_at'
        ]
        read_only_fields = ['id', 'payer', 'payee', 'created_at']

    def validate_payer_id(self, value):
        """Validate that payer exists."""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Payer does not exist.")
        return value

    def validate_payee_id(self, value):
        """Validate that payee exists."""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Payee does not exist.")
        return value

    def validate(self, data):
        """Validate that payer and payee are different and both are group members."""
        payer_id = data.get('payer_id')
        payee_id = data.get('payee_id')
        group = data.get('group')

        if payer_id == payee_id:
            raise serializers.ValidationError("Payer and payee cannot be the same user.")

        # Check that both users are members of the group
        if not Membership.objects.filter(group=group, user_id=payer_id).exists():
            raise serializers.ValidationError("Payer is not a member of this group.")

        if not Membership.objects.filter(group=group, user_id=payee_id).exists():
            raise serializers.ValidationError("Payee is not a member of this group.")

        return data

    def create(self, validated_data):
        """Create settlement with proper user assignment."""
        payer_id = validated_data.pop('payer_id')
        payee_id = validated_data.pop('payee_id')

        settlement = Settlement.objects.create(
            payer_id=payer_id,
            payee_id=payee_id,
            **validated_data
        )

        return settlement
