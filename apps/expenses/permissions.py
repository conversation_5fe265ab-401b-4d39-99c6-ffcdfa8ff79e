"""
Custom permissions for expenses app.
"""
from rest_framework import permissions
from .models import Group, Membership


class IsGroupMember(permissions.BasePermission):
    """
    Permission to check if user is a member of the group.
    """
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user is a member of the group related to the object.
        """
        # Determine the group based on the object type
        if isinstance(obj, Group):
            group = obj
        elif hasattr(obj, 'group'):
            group = obj.group
        else:
            return False
        
        # Check if user is a member of the group
        return Membership.objects.filter(
            user=request.user,
            group=group
        ).exists()


class IsGroupAdmin(permissions.BasePermission):
    """
    Permission to check if user is an admin of the group.
    """
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user is an admin of the group related to the object.
        """
        # Determine the group based on the object type
        if isinstance(obj, Group):
            group = obj
        elif hasattr(obj, 'group'):
            group = obj.group
        else:
            return False
        
        # Check if user is an admin of the group
        return Membership.objects.filter(
            user=request.user,
            group=group,
            role='admin'
        ).exists()


class IsGroupMemberOrReadOnly(permissions.BasePermission):
    """
    Permission to allow read access to group members and write access to admins.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for group members
        if request.method in permissions.SAFE_METHODS:
            return IsGroupMember().has_object_permission(request, view, obj)
        
        # Write permissions for group admins
        return IsGroupAdmin().has_object_permission(request, view, obj)
