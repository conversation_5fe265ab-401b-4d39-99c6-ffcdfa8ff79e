"""
Tests for email/password authentication functionality.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.accounts.models import Authentication<PERSON>rovider, PasswordResetToken
import json

User = get_user_model()


class EmailPasswordAuthenticationTest(APITestCase):
    """Test email/password authentication endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.register_url = reverse('authx:register')
        self.login_url = reverse('authx:login')
        self.profile_url = reverse('authx:profile')
        self.profile_update_url = reverse('authx:update_profile')
        
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        self.login_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }
    
    def test_user_registration_success(self):
        """Test successful user registration."""
        response = self.client.post(self.register_url, self.user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('user', response.data)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('message', response.data)
        
        # Check user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        
        # Check authentication provider was created
        auth_provider = AuthenticationProvider.objects.get(user=user, provider='email')
        self.assertEqual(auth_provider.provider_email, '<EMAIL>')
        self.assertTrue(auth_provider.is_primary)
        self.assertFalse(auth_provider.is_verified)
    
    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email fails."""
        # Create first user
        self.client.post(self.register_url, self.user_data, format='json')
        
        # Try to create second user with same email
        response = self.client.post(self.register_url, self.user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data['details'])
    
    def test_user_registration_weak_password(self):
        """Test registration with weak password fails."""
        data = self.user_data.copy()
        data['password'] = '123'
        
        response = self.client.post(self.register_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data['details'])
    
    def test_user_login_success(self):
        """Test successful user login."""
        # Create user first
        self.client.post(self.register_url, self.user_data, format='json')
        
        # Login
        response = self.client.post(self.login_url, self.login_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        
        # Check that last_used_at was updated for auth provider
        user = User.objects.get(email='<EMAIL>')
        auth_provider = AuthenticationProvider.objects.get(user=user, provider='email')
        self.assertIsNotNone(auth_provider.last_used_at)
    
    def test_user_login_invalid_email(self):
        """Test login with invalid email fails."""
        data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data['details'])
    
    def test_user_login_invalid_password(self):
        """Test login with invalid password fails."""
        # Create user first
        self.client.post(self.register_url, self.user_data, format='json')
        
        # Try login with wrong password
        data = {
            'email': '<EMAIL>',
            'password': 'WrongPassword123!'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data['details'])
    
    def test_profile_endpoint_authenticated(self):
        """Test profile endpoint with authentication."""
        # Create and login user
        self.client.post(self.register_url, self.user_data, format='json')
        login_response = self.client.post(self.login_url, self.login_data, format='json')
        
        # Access profile with token
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['email'], '<EMAIL>')
        self.assertEqual(len(response.data['user']['auth_providers']), 1)
    
    def test_profile_endpoint_unauthenticated(self):
        """Test profile endpoint without authentication fails."""
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_profile_update_success(self):
        """Test successful profile update."""
        # Register and login user
        self.client.post(self.register_url, self.user_data, format='json')
        login_response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }, format='json')

        access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')

        # Update profile
        update_data = {
            'username': 'newusername',
            'first_name': 'NewFirst',
            'profile_photo': 'https://example.com/photo.jpg'
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['user']['username'], 'newusername')
        self.assertEqual(response.data['user']['first_name'], 'NewFirst')
        self.assertEqual(response.data['user']['profile_photo'], 'https://example.com/photo.jpg')

    def test_profile_update_partial(self):
        """Test partial profile update."""
        # Register and login user
        self.client.post(self.register_url, self.user_data, format='json')
        login_response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }, format='json')

        access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')

        # Update only username
        update_data = {
            'username': 'partialusername'
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user']['username'], 'partialusername')
        # Other fields should remain unchanged
        self.assertEqual(response.data['user']['first_name'], 'Test')
        self.assertEqual(response.data['user']['last_name'], 'User')

    def test_profile_update_duplicate_username(self):
        """Test profile update with duplicate username fails."""
        # Create first user
        self.client.post(self.register_url, self.user_data, format='json')

        # Create second user
        user_data_2 = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'first_name': 'Test2',
            'last_name': 'User2'
        }
        self.client.post(self.register_url, user_data_2, format='json')

        # Login as second user
        login_response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }, format='json')

        access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')

        # Try to update username to first user's username
        update_data = {
            'username': 'test'  # First user's username
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', response.data['details'])

    def test_profile_update_unauthenticated(self):
        """Test profile update without authentication fails."""
        update_data = {
            'username': 'newusername'
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_username_generation(self):
        """Test that username is generated from email."""
        response = self.client.post(self.register_url, self.user_data, format='json')
        
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.username, 'test')  # Should be extracted from email
    
    def test_username_uniqueness(self):
        """Test that usernames are made unique when conflicts occur."""
        # Create first user
        self.client.post(self.register_url, self.user_data, format='json')
        
        # Create second user with same email prefix
        data2 = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'first_name': 'Test2',
            'last_name': 'User2'
        }
        
        # This should work but create a different username
        response = self.client.post(self.register_url, data2, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        user2 = User.objects.get(email='<EMAIL>')
        self.assertEqual(user2.username, 'test2')  # Should be different from first user
