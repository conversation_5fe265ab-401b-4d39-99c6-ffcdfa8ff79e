/**
 * Telegram WebApp utility functions
 */

/**
 * Initialize Telegram WebApp
 */
export function initTelegramWebApp() {
  if (window.Telegram?.WebApp) {
    const tg = window.Telegram.WebApp;
    
    // Initialize the app
    tg.ready();
    tg.expand();
    
    // Set up theme
    const isDark = tg.colorScheme === 'dark';
    document.body.classList.toggle('dark-theme', isDark);
    
    return tg;
  }
  
  // Return mock object for development
  return {
    initData: '',
    initDataUnsafe: {},
    colorScheme: 'light',
    themeParams: {},
    isExpanded: true,
    viewportHeight: window.innerHeight,
    viewportStableHeight: window.innerHeight,
    BackButton: {
      isVisible: false,
      onClick: () => {},
      show: () => {},
      hide: () => {}
    },
    MainButton: {
      text: '',
      color: '#2481cc',
      textColor: '#ffffff',
      isVisible: false,
      isProgressVisible: false,
      isActive: true,
      setText: () => {},
      onClick: () => {},
      show: () => {},
      hide: () => {}
    },
    ready: () => {},
    expand: () => {},
    close: () => {},
    sendData: () => {},
    showAlert: (message) => alert(message),
    showConfirm: (message) => confirm(message),
    showPopup: () => {},
    showScanQrPopup: () => {},
    closeScanQrPopup: () => {},
    readTextFromClipboard: () => Promise.resolve(''),
    requestWriteAccess: () => Promise.resolve(false),
    requestContact: () => Promise.resolve(false),
    invokeCustomMethod: () => Promise.resolve({})
  };
}

/**
 * Get Telegram WebApp instance
 */
export function getTelegramWebApp() {
  return window.Telegram?.WebApp || initTelegramWebApp();
}

/**
 * Check if running in Telegram WebApp
 */
export function isTelegramWebApp() {
  return !!window.Telegram?.WebApp;
}

/**
 * Get Telegram theme colors
 */
export function getTelegramTheme() {
  const tg = getTelegramWebApp();
  const isDark = tg.colorScheme === 'dark';
  
  return {
    isDark,
    colors: {
      bg_color: tg.themeParams.bg_color || (isDark ? '#212121' : '#ffffff'),
      text_color: tg.themeParams.text_color || (isDark ? '#ffffff' : '#000000'),
      hint_color: tg.themeParams.hint_color || (isDark ? '#708499' : '#999999'),
      link_color: tg.themeParams.link_color || '#2481cc',
      button_color: tg.themeParams.button_color || '#2481cc',
      button_text_color: tg.themeParams.button_text_color || '#ffffff',
      secondary_bg_color: tg.themeParams.secondary_bg_color || (isDark ? '#181818' : '#f1f1f1')
    }
  };
}

/**
 * Show Telegram back button
 */
export function showBackButton(onClick) {
  const tg = getTelegramWebApp();
  tg.BackButton.onClick(onClick);
  tg.BackButton.show();
}

/**
 * Hide Telegram back button
 */
export function hideBackButton() {
  const tg = getTelegramWebApp();
  tg.BackButton.hide();
}

/**
 * Initialize back button with cleanup
 */
export function initBackButton(onClick) {
  showBackButton(onClick);

  // Return cleanup function
  return () => {
    hideBackButton();
  };
}

/**
 * Show Telegram main button
 */
export function showMainButton(text, onClick) {
  const tg = getTelegramWebApp();
  tg.MainButton.setText(text);
  tg.MainButton.onClick(onClick);
  tg.MainButton.show();
}

/**
 * Hide Telegram main button
 */
export function hideMainButton() {
  const tg = getTelegramWebApp();
  tg.MainButton.hide();
}
