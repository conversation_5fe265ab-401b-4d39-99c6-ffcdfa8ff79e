/* Authentication Forms Styles */

.auth-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.auth-form__header {
  text-align: center;
  margin-bottom: 24px;
}

.auth-form__header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000);
  margin: 0 0 8px 0;
}

.auth-form__header p {
  font-size: 14px;
  color: var(--tg-theme-hint-color, #999);
  margin: 0;
  line-height: 1.4;
}

.auth-form__error {
  background-color: var(--tg-theme-destructive-text-color, #ff3b30);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 16px;
  text-align: center;
}

.auth-form__success {
  text-align: center;
  padding: 24px 16px;
}

.auth-form__success-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.auth-form__success p {
  font-size: 14px;
  color: var(--tg-theme-text-color, #000);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.auth-form__hint {
  font-size: 12px;
  color: var(--tg-theme-hint-color, #999) !important;
}

.auth-form__fields {
  margin-bottom: 24px;
}

.auth-form__fields .tg-input-wrapper {
  margin-bottom: 16px;
}

.auth-form__fields .tg-input-wrapper:last-child {
  margin-bottom: 0;
}

.auth-form__name-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.auth-form__actions {
  margin-top: 24px;
}

.auth-form__submit {
  width: 100%;
}

/* Auth tabs styles */
.auth-tabs {
  display: flex;
  background-color: var(--tg-theme-secondary-bg-color, #f1f1f1);
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 24px;
}

.auth-tabs__tab {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: none;
  color: var(--tg-theme-hint-color, #999);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-tabs__tab--active {
  background-color: var(--tg-theme-bg-color, #fff);
  color: var(--tg-theme-text-color, #000);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.auth-tabs__tab:hover:not(.auth-tabs__tab--active) {
  color: var(--tg-theme-text-color, #000);
}

/* Auth method selector */
.auth-methods {
  margin-bottom: 24px;
}

.auth-method {
  display: block;
  width: 100%;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid var(--tg-theme-secondary-bg-color, #f1f1f1);
  border-radius: 8px;
  background-color: var(--tg-theme-bg-color, #fff);
  color: var(--tg-theme-text-color, #000);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.auth-method:hover {
  border-color: var(--tg-theme-button-color, #007aff);
  background-color: var(--tg-theme-secondary-bg-color, #f8f9fa);
}

.auth-method:last-child {
  margin-bottom: 0;
}

.auth-method__content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auth-method__icon {
  font-size: 24px;
  width: 32px;
  text-align: center;
}

.auth-method__text {
  flex: 1;
}

.auth-method__title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.auth-method__description {
  font-size: 14px;
  color: var(--tg-theme-hint-color, #999);
  margin: 0;
}

/* Divider */
.auth-divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  color: var(--tg-theme-hint-color, #999);
  font-size: 14px;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--tg-theme-secondary-bg-color, #e5e5e5);
}

.auth-divider::before {
  margin-right: 16px;
}

.auth-divider::after {
  margin-left: 16px;
}

/* Link styles */
.auth-link {
  display: inline-block;
  color: var(--tg-theme-link-color, #007aff);
  text-decoration: none;
  font-size: 14px;
  margin-top: 16px;
}

.auth-link:hover {
  text-decoration: underline;
}

.auth-link--center {
  display: block;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .auth-form__name-fields {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .auth-tabs__tab {
    padding: 10px 12px;
    font-size: 13px;
  }
}
