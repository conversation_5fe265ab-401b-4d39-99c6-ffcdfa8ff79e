import uuid
import secrets
from datetime import <PERSON><PERSON><PERSON>
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import <PERSON>ailValidator
from django.utils import timezone


class User(AbstractUser):
    """
    Custom User model that extends Django's AbstractUser.
    Uses UUID as primary key for better security.
    Supports soft deletion to preserve data integrity in groups and expenses.

    This model serves as the central user identity that can be linked to multiple
    authentication providers (email/password, Telegram, OAuth providers, etc.).
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Make email optional (can be null for auth provider users)
    email = models.EmailField(
        unique=True,
        null=True,
        blank=True,
        validators=[EmailValidator()],
        help_text="User's email address - required for email/password authentication"
    )

    # Override username to make it optional (can be auto-generated)
    username = models.CharField(
        max_length=150,
        unique=True,
        help_text="Unique username - auto-generated if not provided"
    )

    # Profile information - nullable for flexibility
    first_name = models.CharField(
        max_length=150,
        blank=True,
        null=True,
        help_text="User's first name"
    )
    last_name = models.CharField(
        max_length=150,
        blank=True,
        null=True,
        help_text="User's last name"
    )
    profile_photo = models.URLField(
        blank=True,
        null=True,
        help_text="URL to user's profile photo"
    )

    is_published = models.BooleanField(
        default=True,
        help_text="Whether this user should be visible in API responses"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Soft delete flag - user is marked as deleted but not removed to preserve group/expense integrity"
    )

    # Track when user was created and last updated
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'auth_user'  # Keep the same table name as default User

    def save(self, *args, **kwargs):
        """Override save to auto-generate username if not provided."""
        if not self.username:
            # Generate username from email or UUID
            if self.email:
                base_username = self.email.split('@')[0]
            else:
                base_username = f"user_{str(self.id)[:8]}"

            # Ensure username is unique
            counter = 1
            original_username = base_username
            while User.objects.filter(username=base_username).exclude(id=self.id).exists():
                base_username = f"{original_username}_{counter}"
                counter += 1

            self.username = base_username

        super().save(*args, **kwargs)


class AuthenticationProvider(models.Model):
    """
    Model to track different authentication providers and their linkage to user accounts.
    This enables account linking/merging functionality.
    """
    PROVIDER_CHOICES = [
        ('email', 'Email/Password'),
        ('telegram', 'Telegram'),
        ('google', 'Google OAuth'),
        ('github', 'GitHub OAuth'),
        ('apple', 'Apple ID'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='auth_providers'
    )
    provider = models.CharField(
        max_length=20,
        choices=PROVIDER_CHOICES,
        help_text="The authentication provider type"
    )
    provider_user_id = models.CharField(
        max_length=255,
        help_text="User ID from the external provider (e.g., Telegram user ID, Google sub claim)"
    )
    provider_username = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Username from the external provider"
    )
    provider_email = models.EmailField(
        blank=True,
        null=True,
        help_text="Email from the external provider"
    )
    is_primary = models.BooleanField(
        default=False,
        help_text="Whether this is the primary authentication method for the user"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this authentication provider has been verified"
    )
    # Publication and deletion control
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this provider should be visible in API responses"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Soft delete flag - provider is marked as deleted but not removed"
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this provider was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="When this provider was last updated"
    )
    linked_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this provider was linked to the user account"
    )
    last_used_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this provider was last used for authentication"
    )

    # Additional provider-specific data (JSON field for flexibility)
    provider_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional provider-specific data (profile info, tokens, etc.)"
    )

    class Meta:
        db_table = 'auth_providers'
        unique_together = [('provider', 'provider_user_id')]
        verbose_name = 'Authentication Provider'
        verbose_name_plural = 'Authentication Providers'
        indexes = [
            models.Index(fields=['user', 'provider']),
            models.Index(fields=['provider', 'provider_user_id']),
            models.Index(fields=['provider_email']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_provider_display()}"





class PasswordResetToken(models.Model):
    """
    Model to store password reset tokens.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='password_reset_tokens'
    )
    token = models.CharField(
        max_length=100,
        unique=True,
        help_text="Password reset token"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(
        help_text="Token expiration time"
    )
    used_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the token was used"
    )

    class Meta:
        db_table = 'password_reset_tokens'
        verbose_name = 'Password Reset Token'
        verbose_name_plural = 'Password Reset Tokens'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'created_at']),
        ]

    def save(self, *args, **kwargs):
        """Override save to set expiration time and generate token."""
        if not self.token:
            self.token = secrets.token_urlsafe(32)

        if not self.expires_at:
            # Token expires in 1 hour
            self.expires_at = timezone.now() + timedelta(hours=1)

        super().save(*args, **kwargs)

    def is_valid(self):
        """Check if token is still valid."""
        return (
            not self.used_at and
            timezone.now() < self.expires_at
        )

    def mark_as_used(self):
        """Mark token as used."""
        self.used_at = timezone.now()
        self.save()

    def __str__(self):
        return f"Password reset token for {self.user.email}"
