import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card from '../../components/ui/Card';
import Cell from '../../components/ui/Cell';
import './ProfileScreen.css';

export default function ProfileScreen() {
  const { user, getProfile, updateProfile, logout, isLoading, error } = useAuth();
  const navigate = useNavigate();
  
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: ''
  });
  const [fieldErrors, setFieldErrors] = useState({});

  useEffect(() => {
    // Load fresh profile data
    const loadProfile = async () => {
      try {
        await getProfile();
      } catch (error) {
        console.error('Failed to load profile:', error);
      }
    };
    
    loadProfile();
  }, [getProfile]);

  useEffect(() => {
    // Update form data when user data changes
    if (user) {
      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || ''
      });
    }
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      await updateProfile(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update failed:', error);
    }
  };

  const handleCancel = () => {
    // Reset form data to original user data
    if (user) {
      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || ''
      });
    }
    setFieldErrors({});
    setIsEditing(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getAuthProviders = () => {
    if (!user?.auth_providers) return [];
    return user.auth_providers;
  };

  const getPrimaryProvider = () => {
    const providers = getAuthProviders();
    return providers.find(p => p.is_primary) || providers[0];
  };

  if (!user) {
    return (
      <div className="profile-screen">
        <div className="profile-screen__loading">
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="profile-screen">
      <div className="profile-screen__header">
        <Button
          variant="ghost"
          onClick={() => navigate('/')}
          className="profile-screen__back"
        >
          ← Back
        </Button>
        <h1>Profile</h1>
      </div>

      <div className="profile-screen__content">
        <Card className="profile-screen__card">
          <div className="profile-screen__avatar">
            <div className="profile-screen__avatar-circle">
              {user.first_name?.[0]?.toUpperCase() || '👤'}
            </div>
            <h2>{user.first_name} {user.last_name}</h2>
            <p className="profile-screen__email">{user.email}</p>
          </div>

          {error && (
            <div className="profile-screen__error">
              {error}
            </div>
          )}

          {isEditing ? (
            <div className="profile-screen__form">
              <div className="profile-screen__fields">
                <Input
                  name="first_name"
                  type="text"
                  label="First Name"
                  placeholder="Enter your first name"
                  value={formData.first_name}
                  onChange={handleChange}
                  error={fieldErrors.first_name}
                  disabled={isLoading}
                />

                <Input
                  name="last_name"
                  type="text"
                  label="Last Name"
                  placeholder="Enter your last name"
                  value={formData.last_name}
                  onChange={handleChange}
                  error={fieldErrors.last_name}
                  disabled={isLoading}
                />

                <Input
                  name="email"
                  type="email"
                  label="Email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  error={fieldErrors.email}
                  disabled={isLoading}
                />
              </div>

              <div className="profile-screen__actions">
                <Button
                  variant="primary"
                  onClick={handleSave}
                  loading={isLoading}
                  className="profile-screen__save"
                >
                  Save Changes
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="profile-screen__info">
              <div className="profile-screen__section">
                <h3>Account Information</h3>
                <Cell
                  title="Name"
                  subtitle={`${user.first_name} ${user.last_name}`.trim() || 'Not set'}
                />
                <Cell
                  title="Email"
                  subtitle={user.email || 'Not set'}
                />
                <Cell
                  title="Member since"
                  subtitle={new Date(user.date_joined).toLocaleDateString()}
                />
              </div>

              <div className="profile-screen__section">
                <h3>Authentication</h3>
                {getAuthProviders().map((provider) => (
                  <Cell
                    key={provider.id}
                    title={provider.provider === 'email' ? 'Email' : 'Telegram'}
                    subtitle={provider.provider_email || provider.provider_username || 'Connected'}
                    badge={provider.is_primary ? 'Primary' : null}
                  />
                ))}
              </div>

              <div className="profile-screen__actions">
                <Button
                  variant="primary"
                  onClick={() => setIsEditing(true)}
                  className="profile-screen__edit"
                >
                  Edit Profile
                </Button>
                
                {getPrimaryProvider()?.provider === 'email' && (
                  <Button
                    variant="ghost"
                    onClick={() => navigate('/change-password')}
                  >
                    Change Password
                  </Button>
                )}
              </div>
            </div>
          )}

          <div className="profile-screen__danger-zone">
            <Button
              variant="destructive"
              onClick={handleLogout}
              className="profile-screen__logout"
            >
              Sign Out
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
