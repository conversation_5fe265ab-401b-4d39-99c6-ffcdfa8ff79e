"""
Tests for expenses services.
"""
from django.test import TestCase
from apps.accounts.models import User
from apps.expenses.models import Group, Membership, Expense, ExpenseParticipant, Settlement
from apps.expenses.services import (
    group_net_balances, 
    simplify_transfers, 
    get_user_balance_in_group,
    get_group_summary
)


class GroupNetBalancesTest(TestCase):
    """Test cases for group_net_balances service."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user3 = User.objects.create_user(
            username='user3',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        
        # Add members to group
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')
        Membership.objects.create(user=self.user3, group=self.group, role='member')

    def test_group_net_balances_no_expenses(self):
        """Test balances calculation with no expenses."""
        balances = group_net_balances(self.group)
        
        # All balances should be 0
        self.assertEqual(len(balances), 3)
        for user_id, balance in balances.items():
            self.assertEqual(balance, 0)

    def test_group_net_balances_with_expenses(self):
        """Test balances calculation with expenses."""
        # Create an expense
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Dinner',
            amount=6000,  # 60.00
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        # User1 paid everything, others owe equal shares
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=2000,  # owes 20.00
            paid=6000    # paid 60.00
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=2000,  # owes 20.00
            paid=0       # paid 0.00
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user3,
            share=2000,  # owes 20.00
            paid=0       # paid 0.00
        )
        
        balances = group_net_balances(self.group)
        
        # User1 should be owed 40.00 (paid 60.00 - owes 20.00)
        # User2 and User3 should owe 20.00 each
        user1_balance = balances[str(self.user1.id)]
        user2_balance = balances[str(self.user2.id)]
        user3_balance = balances[str(self.user3.id)]
        
        self.assertEqual(user1_balance, 4000)  # +40.00
        self.assertEqual(user2_balance, -2000)  # -20.00
        self.assertEqual(user3_balance, -2000)  # -20.00

    def test_group_net_balances_with_settlements(self):
        """Test balances calculation with settlements."""
        # Create expense first
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Dinner',
            amount=6000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=2000,
            paid=6000
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=2000,
            paid=0
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user3,
            share=2000,
            paid=0
        )
        
        # User2 pays back User1
        Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount=2000,  # 20.00
            currency='USD',
            settled_at='2025-09-22T13:00:00Z'
        )
        
        balances = group_net_balances(self.group)
        
        # After settlement:
        # User1: was owed 40.00, received 20.00, now owed 20.00
        # User2: owed 20.00, paid 20.00, now owes 0.00
        # User3: still owes 20.00
        user1_balance = balances[str(self.user1.id)]
        user2_balance = balances[str(self.user2.id)]
        user3_balance = balances[str(self.user3.id)]
        
        self.assertEqual(user1_balance, 2000)   # +20.00
        self.assertEqual(user2_balance, 0)      # 0.00
        self.assertEqual(user3_balance, -2000)  # -20.00


class SimplifyTransfersTest(TestCase):
    """Test cases for simplify_transfers service."""

    def test_simplify_transfers_simple_case(self):
        """Test transfer simplification with simple case."""
        balances = {
            'user1': 4000,   # owed 40.00
            'user2': -2000,  # owes 20.00
            'user3': -2000   # owes 20.00
        }
        
        transfers = simplify_transfers(balances)
        
        # Should have 2 transfers
        self.assertEqual(len(transfers), 2)
        
        # Check transfers (order might vary due to sorting)
        total_to_user1 = 0
        for from_user, to_user, amount in transfers:
            if to_user == 'user1':
                total_to_user1 += amount
        
        self.assertEqual(total_to_user1, 4000)  # User1 should receive 40.00 total

    def test_simplify_transfers_complex_case(self):
        """Test transfer simplification with complex case."""
        balances = {
            'user1': 3000,   # owed 30.00
            'user2': 1000,   # owed 10.00
            'user3': -2000,  # owes 20.00
            'user4': -2000   # owes 20.00
        }
        
        transfers = simplify_transfers(balances)
        
        # Verify total amounts balance out
        total_in = sum(amount for _, _, amount in transfers)
        total_out = sum(max(0, -balance) for balance in balances.values())
        
        self.assertEqual(total_in, total_out)

    def test_simplify_transfers_already_balanced(self):
        """Test transfer simplification when already balanced."""
        balances = {
            'user1': 0,
            'user2': 0,
            'user3': 0
        }
        
        transfers = simplify_transfers(balances)
        
        # Should have no transfers
        self.assertEqual(len(transfers), 0)


class GetUserBalanceInGroupTest(TestCase):
    """Test cases for get_user_balance_in_group service."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')

    def test_get_user_balance_no_expenses(self):
        """Test getting user balance with no expenses."""
        balance = get_user_balance_in_group(str(self.user1.id), self.group)
        self.assertEqual(balance, 0)

    def test_get_user_balance_with_expenses(self):
        """Test getting user balance with expenses."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=1000,
            paid=2000
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=1000,
            paid=0
        )
        
        balance1 = get_user_balance_in_group(str(self.user1.id), self.group)
        balance2 = get_user_balance_in_group(str(self.user2.id), self.group)
        
        self.assertEqual(balance1, 1000)   # owed 10.00
        self.assertEqual(balance2, -1000)  # owes 10.00


class GetGroupSummaryTest(TestCase):
    """Test cases for get_group_summary service."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')

    def test_get_group_summary_empty_group(self):
        """Test group summary with no expenses or settlements."""
        summary = get_group_summary(self.group)
        
        self.assertEqual(summary['group_id'], str(self.group.id))
        self.assertEqual(summary['group_name'], 'Test Group')
        self.assertEqual(summary['member_count'], 2)
        self.assertEqual(summary['total_expenses'], 0)
        self.assertEqual(summary['total_settlements'], 0)
        self.assertEqual(summary['expense_count'], 0)
        self.assertEqual(summary['settlement_count'], 0)
        self.assertTrue(summary['is_settled'])

    def test_get_group_summary_with_data(self):
        """Test group summary with expenses and settlements."""
        # Create expense
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=1000,
            paid=2000
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=1000,
            paid=0
        )
        
        # Create settlement
        Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount=1000,
            currency='USD',
            settled_at='2025-09-22T13:00:00Z'
        )
        
        summary = get_group_summary(self.group)
        
        self.assertEqual(summary['total_expenses'], 2000)
        self.assertEqual(summary['total_settlements'], 1000)
        self.assertEqual(summary['expense_count'], 1)
        self.assertEqual(summary['settlement_count'], 1)
        self.assertTrue(summary['is_settled'])  # Should be settled after payment
