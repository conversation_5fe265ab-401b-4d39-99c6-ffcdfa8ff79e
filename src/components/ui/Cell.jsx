import './Cell.css';

/**
 * Telegram-style Cell component for list items
 */
export default function Cell({
  children,
  title,
  subtitle,
  description,
  badge,
  before,
  after,
  onClick,
  disabled = false,
  className = '',
  ...props
}) {
  const baseClass = 'tg-cell';
  const clickableClass = onClick ? 'tg-cell--clickable' : '';
  const disabledClass = disabled ? 'tg-cell--disabled' : '';
  
  const classes = [
    baseClass,
    clickableClass,
    disabledClass,
    className
  ].filter(Boolean).join(' ');

  const handleClick = () => {
    if (onClick && !disabled) {
      onClick();
    }
  };

  return (
    <div
      className={classes}
      onClick={handleClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick && !disabled ? 0 : undefined}
      {...props}
    >
      {before && (
        <div className="tg-cell__before">
          {before}
        </div>
      )}
      
      <div className="tg-cell__content">
        {title && (
          <div className="tg-cell__title">
            {title}
            {badge && (
              <span className="tg-cell__badge">
                {badge}
              </span>
            )}
          </div>
        )}

        {subtitle && (
          <div className="tg-cell__subtitle">
            {subtitle}
          </div>
        )}
        
        {description && (
          <div className="tg-cell__description">
            {description}
          </div>
        )}
        
        {children && (
          <div className="tg-cell__children">
            {children}
          </div>
        )}
      </div>
      
      {after && (
        <div className="tg-cell__after">
          {after}
        </div>
      )}
    </div>
  );
}
