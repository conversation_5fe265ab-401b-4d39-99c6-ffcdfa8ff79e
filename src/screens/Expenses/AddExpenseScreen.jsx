import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { groupsAPI, expensesAPI } from '../../services/api';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Cell from '../../components/ui/Cell';
import Card from '../../components/ui/Card';
import { formatCurrency, parseCurrencyInput, toMinorUnits } from '../../utils/currency';
import { initBackButton } from '../../utils/telegram';
import './AddExpenseScreen.css';

export default function AddExpenseScreen() {
  const { groupId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [group, setGroup] = useState(null);
  const [members, setMembers] = useState([]);
  const [selectedParticipants, setSelectedParticipants] = useState(new Set());
  const [splitType, setSplitType] = useState('equal'); // 'equal', 'custom'
  const [customSplits, setCustomSplits] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm({
    defaultValues: {
      description: '',
      amount: '',
      paid_by: user?.id
    }
  });

  const watchedAmount = watch('amount');

  useEffect(() => {
    // Initialize Telegram back button
    const cleanup = initBackButton(() => {
      navigate(`/groups/${groupId}`);
    });

    loadGroupData();

    return cleanup;
  }, [groupId, navigate]);

  useEffect(() => {
    // Auto-select current user as participant
    if (user?.id && members.length > 0) {
      setSelectedParticipants(new Set([user.id]));
    }
  }, [user?.id, members]);

  const loadGroupData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [groupData, membersData] = await Promise.all([
        groupsAPI.getGroup(groupId),
        groupsAPI.getGroupMembers(groupId)
      ]);

      setGroup(groupData);
      setMembers(membersData);
    } catch (error) {
      console.error('Failed to load group data:', error);
      setError('Failed to load group data');
    } finally {
      setLoading(false);
    }
  };

  const toggleParticipant = (memberId) => {
    const newSelected = new Set(selectedParticipants);
    if (newSelected.has(memberId)) {
      newSelected.delete(memberId);
    } else {
      newSelected.add(memberId);
    }
    setSelectedParticipants(newSelected);

    // Reset custom splits when participants change
    if (splitType === 'custom') {
      setCustomSplits({});
    }
  };

  const handleSplitTypeChange = (type) => {
    setSplitType(type);
    setCustomSplits({});
  };

  const updateCustomSplit = (memberId, amount) => {
    setCustomSplits(prev => ({
      ...prev,
      [memberId]: amount
    }));
  };

  const calculateSplits = () => {
    const amount = parseCurrencyInput(watchedAmount);
    const participantIds = Array.from(selectedParticipants);

    if (splitType === 'equal') {
      const splitAmount = amount / participantIds.length;
      return participantIds.reduce((acc, id) => {
        acc[id] = splitAmount;
        return acc;
      }, {});
    } else {
      return customSplits;
    }
  };

  const validateSplits = () => {
    const amount = parseCurrencyInput(watchedAmount);
    const splits = calculateSplits();
    const totalSplit = Object.values(splits).reduce((sum, split) => sum + (split || 0), 0);

    return Math.abs(amount - totalSplit) < 0.01; // Allow for small rounding differences
  };

  const onSubmit = async (data) => {
    try {
      setSubmitting(true);

      if (selectedParticipants.size === 0) {
        throw new Error('Please select at least one participant');
      }

      if (splitType === 'custom' && !validateSplits()) {
        throw new Error('Custom splits must add up to the total amount');
      }

      const splits = calculateSplits();
      const expenseData = {
        description: data.description,
        amount: toMinorUnits(parseCurrencyInput(data.amount)),
        paid_by: data.paid_by,
        participants: Array.from(selectedParticipants).map(participantId => ({
          user_id: participantId,
          amount: toMinorUnits(splits[participantId])
        }))
      };

      await expensesAPI.createExpense(groupId, expenseData);
      navigate(`/groups/${groupId}`);
    } catch (error) {
      console.error('Failed to create expense:', error);
      setError(error.message || 'Failed to create expense');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="screen">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  if (error && !group) {
    return (
      <div className="screen">
        <div className="error">
          <p>{error}</p>
          <Button onClick={loadGroupData}>Try Again</Button>
        </div>
      </div>
    );
  }

  const splits = calculateSplits();
  const totalAmount = parseCurrencyInput(watchedAmount);

  return (
    <div className="screen">
      <div className="screen__header">
        <h1 className="screen__title">Add Expense</h1>
      </div>

      <div className="screen__content">
        <div className="container">
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Basic Info */}
            <Card className="add-expense__basic-info">
              <div className="form-field">
                <Input
                  label="Description"
                  placeholder="What was this expense for?"
                  error={errors.description?.message}
                  {...register('description', {
                    required: 'Description is required',
                    maxLength: {
                      value: 100,
                      message: 'Description must be less than 100 characters'
                    }
                  })}
                />
              </div>

              <div className="form-field">
                <Input
                  label="Amount"
                  placeholder="0.00"
                  type="number"
                  step="0.01"
                  min="0"
                  error={errors.amount?.message}
                  {...register('amount', {
                    required: 'Amount is required',
                    min: {
                      value: 0.01,
                      message: 'Amount must be greater than 0'
                    }
                  })}
                />
              </div>

              <div className="form-field">
                <label className="tg-input__label">Paid by</label>
                <div className="section__content">
                  {members.map((member) => (
                    <Cell
                      key={member.id}
                      title={`${member.first_name} ${member.last_name || ''}`.trim()}
                      subtitle={member.username ? `@${member.username}` : ''}
                      before={
                        <input
                          type="radio"
                          value={member.id}
                          {...register('paid_by', { required: 'Please select who paid' })}
                        />
                      }
                    />
                  ))}
                </div>
              </div>
            </Card>

            {/* Participants */}
            <div className="section">
              <div className="section__header">
                Split between ({selectedParticipants.size} people)
              </div>
              <div className="section__content">
                {members.map((member) => (
                  <Cell
                    key={member.id}
                    title={`${member.first_name} ${member.last_name || ''}`.trim()}
                    subtitle={member.username ? `@${member.username}` : ''}
                    before={
                      <input
                        type="checkbox"
                        checked={selectedParticipants.has(member.id)}
                        onChange={() => toggleParticipant(member.id)}
                      />
                    }
                    after={
                      selectedParticipants.has(member.id) && totalAmount > 0 && (
                        <div className="participant-split">
                          {splitType === 'equal'
                            ? formatCurrency(totalAmount / selectedParticipants.size)
                            : (
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                value={customSplits[member.id] || ''}
                                onChange={(e) => updateCustomSplit(member.id, parseFloat(e.target.value) || 0)}
                              />
                            )
                          }
                        </div>
                      )
                    }
                  />
                ))}
              </div>
            </div>

            {/* Split Type */}
            <Card className="add-expense__split-options">
              <div className="split-type-selector">
                <Button
                  type="button"
                  variant={splitType === 'equal' ? 'primary' : 'secondary'}
                  size="small"
                  onClick={() => handleSplitTypeChange('equal')}
                >
                  Split Equally
                </Button>
                <Button
                  type="button"
                  variant={splitType === 'custom' ? 'primary' : 'secondary'}
                  size="small"
                  onClick={() => handleSplitTypeChange('custom')}
                >
                  Custom Split
                </Button>
              </div>
            </Card>

            {/* Error Display */}
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              size="large"
              loading={submitting}
              disabled={submitting || selectedParticipants.size === 0}
              className="tg-button--full-width"
            >
              Add Expense
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
