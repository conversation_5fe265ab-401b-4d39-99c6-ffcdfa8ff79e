# Spliton-Front

This is a front-end project for the Spliton application.

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`

## Deployment

Run `npm run build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Contributing

Contributions are welcome. Please open an issue or submit a pull request.

---