import { useState } from 'react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import './AuthForms.css';

export default function ForgotPasswordForm({ onSubmit, loading, error, success }) {
  const [email, setEmail] = useState('');
  const [fieldError, setFieldError] = useState('');

  const handleChange = (e) => {
    setEmail(e.target.value);
    
    // Clear field error when user starts typing
    if (fieldError) {
      setFieldError('');
    }
  };

  const validateForm = () => {
    if (!email.trim()) {
      setFieldError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setFieldError('Please enter a valid email address');
      return false;
    }
    
    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    onSubmit(email);
  };

  if (success) {
    return (
      <div className="auth-form">
        <div className="auth-form__header">
          <h2>Check Your Email</h2>
          <p>If an account with this email exists, we've sent you a password reset link.</p>
        </div>

        <div className="auth-form__success">
          <div className="auth-form__success-icon">📧</div>
          <p>Please check your email and follow the instructions to reset your password.</p>
          <p className="auth-form__hint">
            Don't see the email? Check your spam folder or try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="auth-form">
      <div className="auth-form__header">
        <h2>Reset Password</h2>
        <p>Enter your email address and we'll send you a link to reset your password</p>
      </div>

      {error && (
        <div className="auth-form__error">
          {error}
        </div>
      )}

      <div className="auth-form__fields">
        <Input
          name="email"
          type="email"
          label="Email"
          placeholder="Enter your email"
          value={email}
          onChange={handleChange}
          error={fieldError}
          disabled={loading}
        />
      </div>

      <div className="auth-form__actions">
        <Button
          type="submit"
          variant="primary"
          size="large"
          loading={loading}
          className="auth-form__submit"
        >
          Send Reset Link
        </Button>
      </div>
    </form>
  );
}
