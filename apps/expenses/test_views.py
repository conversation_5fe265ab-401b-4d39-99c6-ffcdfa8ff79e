"""
Tests for expenses API views.
"""
import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.models import User
from apps.expenses.models import Group, Membership, Expense, ExpenseParticipant


class ExpenseAPITest(TestCase):
    """Test cases for Expense API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        
        # Create users
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create group
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        
        # Add memberships
        Membership.objects.create(user=self.user1, group=self.group, role='admin')
        Membership.objects.create(user=self.user2, group=self.group, role='member')
        
        # Authenticate as user1
        self.client.force_authenticate(user=self.user1)

    def test_create_expense_with_uuid_fields(self):
        """Test creating expense with UUID-based API."""
        url = reverse('expenses:expense-list')
        data = {
            'group': str(self.group.id),  # UUID string
            'description': 'Test Expense',
            'amount': 5000,  # 50.00 in major units
            'currency': 'USD',
            'occurred_at': '2025-09-22T12:00:00Z',
            'participants': [
                {
                    'user_id': str(self.user1.id),  # UUID string
                    'share': 2500,
                    'paid': 5000
                },
                {
                    'user_id': str(self.user2.id),  # UUID string
                    'share': 2500,
                    'paid': 0
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check response structure
        response_data = response.json()
        self.assertIn('id', response_data)
        self.assertEqual(response_data['description'], 'Test Expense')
        self.assertEqual(response_data['amount'], 5000)
        self.assertEqual(response_data['amount_major_units'], '50.00')
        
        # Check participants structure
        participants = response_data['participants']
        self.assertEqual(len(participants), 2)

        # Find the participant who paid (should have paid > 0)
        paying_participant = None
        for p in participants:
            if p['paid'] > 0:
                paying_participant = p
                break

        self.assertIsNotNone(paying_participant, "Should have a participant who paid")

        # Verify participant fields
        self.assertIn('id', paying_participant)  # ExpenseParticipant UUID
        self.assertIn('user_id', paying_participant)  # User UUID
        self.assertIn('username', paying_participant)
        self.assertEqual(paying_participant['share'], 2500)
        self.assertEqual(paying_participant['paid'], 5000)

    def test_list_expenses_with_uuid_filtering(self):
        """Test listing expenses with UUID-based filtering."""
        # Create expense
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount=3000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=1500,
            paid=3000
        )
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=1500,
            paid=0
        )
        
        # Test listing expenses (user should see expenses from their groups)
        url = reverse('expenses:expense-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        # Response is paginated, so get the results
        results = response_data.get('results', response_data)

        # Should contain at least our expense
        self.assertGreaterEqual(len(results), 1)

        # Find our expense in the results
        expense_found = False
        for expense_data in results:
            if expense_data['id'] == str(expense.id):
                self.assertEqual(expense_data['description'], 'Test Expense')
                expense_found = True
                break

        self.assertTrue(expense_found, "Created expense should be in the list")

    def test_retrieve_expense_by_uuid(self):
        """Test retrieving expense by UUID."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=1000,
            paid=2000
        )
        
        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertEqual(response_data['id'], str(expense.id))
        self.assertEqual(response_data['description'], 'Test Expense')
        self.assertEqual(response_data['amount'], 2000)

    def test_update_expense_with_uuid(self):
        """Test updating expense using UUID."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Original Description',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )

        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        data = {
            'group': str(self.group.id),
            'description': 'Updated Description',
            'amount': 3000,
            'currency': 'USD',
            'occurred_at': '2025-09-22T12:00:00Z'
        }

        # Use PATCH instead of PUT to avoid nested field issues
        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertEqual(response_data['description'], 'Updated Description')
        self.assertEqual(response_data['amount'], 3000)

    def test_delete_expense_by_uuid(self):
        """Test deleting expense by UUID (hard delete)."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='To Delete',
            amount=1000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Expense should be actually deleted (not soft deleted)
        self.assertFalse(Expense.objects.filter(id=expense.id).exists())

    def test_expense_currency_neutral_fields(self):
        """Test that API uses currency-neutral field names."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Currency Test',
            amount=1500,  # Using 'amount' not 'amount_cents'
            currency='EUR',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        participant = ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user1,
            share=750,   # Using 'share' not 'share_cents'
            paid=1500    # Using 'paid' not 'paid_cents'
        )
        
        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        # Check expense fields
        self.assertEqual(response_data['amount'], 1500)
        self.assertEqual(response_data['amount_major_units'], '15.00')

        # Check participant fields
        participant_data = response_data['participants'][0]
        self.assertEqual(participant_data['share'], 750)
        self.assertEqual(participant_data['paid'], 1500)
        self.assertEqual(participant_data['share_major_units'], '7.50')
        self.assertEqual(participant_data['paid_major_units'], '15.00')

    def test_expense_api_security_with_uuid(self):
        """Test that API doesn't expose sequential IDs."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Security Test',
            amount=1000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        expense_id = response_data['id']

        # UUID should be 36 characters with hyphens
        self.assertEqual(len(expense_id), 36)
        self.assertIn('-', expense_id)

        # Should not be a simple integer
        self.assertFalse(expense_id.isdigit())

    def test_unauthorized_access_to_expense(self):
        """Test that unauthorized users cannot access expenses."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Private Expense',
            amount=1000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        # Remove authentication
        self.client.force_authenticate(user=None)

        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.get(url)

        # Should require authentication
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_expense_participant_field_structure(self):
        """Test that expense participants have correct field structure."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Field Test',
            amount=2000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        participant = ExpenseParticipant.objects.create(
            expense=expense,
            user=self.user2,
            share=1000,
            paid=500
        )
        
        url = reverse('expenses:expense-detail', kwargs={'pk': str(expense.id)})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        participant_data = response_data['participants'][0]

        # Check that both id and user_id are present and different
        self.assertIn('id', participant_data)  # ExpenseParticipant UUID
        self.assertIn('user_id', participant_data)  # User UUID
        self.assertNotEqual(participant_data['id'], participant_data['user_id'])

        # Check other required fields
        self.assertIn('username', participant_data)
        self.assertIn('share', participant_data)
        self.assertIn('paid', participant_data)
        self.assertIn('share_major_units', participant_data)
        self.assertIn('paid_major_units', participant_data)
        self.assertIn('net_major_units', participant_data)
