import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card from '../../components/ui/Card';
import './AuthScreen.css';

export default function ResetPasswordScreen() {
  const { confirmPasswordReset, isLoading, error } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [formData, setFormData] = useState({
    email: searchParams.get('email') || '',
    token: searchParams.get('token') || '',
    new_password: '',
    new_password_confirm: ''
  });
  const [fieldErrors, setFieldErrors] = useState({});
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // If no token in URL, redirect to forgot password
    if (!formData.token) {
      navigate('/auth?mode=forgot-password');
    }
  }, [formData.token, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!formData.token.trim()) {
      errors.token = 'Reset token is required';
    }
    
    if (!formData.new_password) {
      errors.new_password = 'New password is required';
    } else if (formData.new_password.length < 8) {
      errors.new_password = 'Password must be at least 8 characters long';
    }
    
    if (!formData.new_password_confirm) {
      errors.new_password_confirm = 'Please confirm your new password';
    } else if (formData.new_password !== formData.new_password_confirm) {
      errors.new_password_confirm = 'Passwords do not match';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await confirmPasswordReset(formData);
      setSuccess(true);
    } catch (error) {
      console.error('Password reset failed:', error);
    }
  };

  if (success) {
    return (
      <div className="auth-screen">
        <div className="auth-screen__content">
          <div className="auth-screen__logo">
            <div className="auth-screen__icon">💰</div>
            <h1 className="auth-screen__title">Spliton</h1>
          </div>

          <Card className="auth-screen__card">
            <div className="auth-form">
              <div className="auth-form__header">
                <h2>Password Reset Successful</h2>
                <p>Your password has been reset successfully.</p>
              </div>

              <div className="auth-form__success">
                <div className="auth-form__success-icon">✅</div>
                <p>You can now sign in with your new password.</p>
              </div>

              <div className="auth-form__actions">
                <Button
                  variant="primary"
                  size="large"
                  onClick={() => navigate('/auth?mode=login')}
                  className="auth-form__submit"
                >
                  Sign In
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-screen">
      <div className="auth-screen__content">
        <div className="auth-screen__logo">
          <div className="auth-screen__icon">💰</div>
          <h1 className="auth-screen__title">Spliton</h1>
        </div>

        <Card className="auth-screen__card">
          <form onSubmit={handleSubmit} className="auth-form">
            <div className="auth-form__header">
              <h2>Reset Your Password</h2>
              <p>Enter your new password below</p>
            </div>

            {error && (
              <div className="auth-form__error">
                {error}
              </div>
            )}

            <div className="auth-form__fields">
              <Input
                name="email"
                type="email"
                label="Email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                error={fieldErrors.email}
                disabled={isLoading}
              />

              <Input
                name="token"
                type="text"
                label="Reset Token"
                placeholder="Enter the token from your email"
                value={formData.token}
                onChange={handleChange}
                error={fieldErrors.token}
                disabled={isLoading}
              />

              <Input
                name="new_password"
                type="password"
                label="New Password"
                placeholder="Enter your new password"
                value={formData.new_password}
                onChange={handleChange}
                error={fieldErrors.new_password}
                disabled={isLoading}
              />

              <Input
                name="new_password_confirm"
                type="password"
                label="Confirm New Password"
                placeholder="Confirm your new password"
                value={formData.new_password_confirm}
                onChange={handleChange}
                error={fieldErrors.new_password_confirm}
                disabled={isLoading}
              />
            </div>

            <div className="auth-form__actions">
              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={isLoading}
                className="auth-form__submit"
              >
                Reset Password
              </Button>
            </div>

            <div className="auth-screen__links">
              <button
                type="button"
                className="auth-link auth-link--center"
                onClick={() => navigate('/auth?mode=login')}
              >
                Back to sign in
              </button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
