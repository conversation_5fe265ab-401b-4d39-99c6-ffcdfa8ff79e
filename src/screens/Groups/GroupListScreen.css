.group-list__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.group-list__user-card {
  margin-bottom: var(--spacing-lg);
}

.group-list__user-card--clickable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.group-list__user-card--clickable:hover {
  background-color: var(--tg-secondary-bg-color, rgba(0, 0, 0, 0.05));
}

.group-list__user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.group-list__user-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--tg-text-color);
}

.group-list__user-username {
  font-size: var(--font-size-sm);
}

.group-list__create-button {
  margin-bottom: var(--spacing-lg);
}
