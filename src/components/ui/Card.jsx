import './Card.css';

/**
 * Telegram-style Card component
 */
export default function Card({
  children,
  title,
  subtitle,
  padding = 'medium',
  onClick,
  className = '',
  ...props
}) {
  const baseClass = 'tg-card';
  const paddingClass = `tg-card--padding-${padding}`;
  const clickableClass = onClick ? 'tg-card--clickable' : '';
  
  const classes = [
    baseClass,
    paddingClass,
    clickableClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      className={classes}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      {...props}
    >
      {(title || subtitle) && (
        <div className="tg-card__header">
          {title && (
            <div className="tg-card__title">
              {title}
            </div>
          )}
          {subtitle && (
            <div className="tg-card__subtitle">
              {subtitle}
            </div>
          )}
        </div>
      )}
      
      <div className="tg-card__content">
        {children}
      </div>
    </div>
  );
}
