import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Button from '../ui/Button';
import Input from '../ui/Input';
import './CreateGroupModal.css';

export default function CreateGroupModal({ isOpen, onClose, onSubmit }) {
  const [loading, setLoading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const handleFormSubmit = async (data) => {
    try {
      setLoading(true);
      await onSubmit(data);
      reset();
      onClose();
    } catch (error) {
      console.error('Failed to create group:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal__header">
          <h2 className="modal__title">Create New Group</h2>
          <button className="modal__close" onClick={handleClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="modal__content">
          <div className="form-field">
            <Input
              label="Group Name"
              placeholder="Enter group name"
              error={errors.name?.message}
              {...register('name', {
                required: 'Group name is required',
                minLength: {
                  value: 2,
                  message: 'Group name must be at least 2 characters'
                },
                maxLength: {
                  value: 50,
                  message: 'Group name must be less than 50 characters'
                }
              })}
            />
          </div>

          <div className="form-field">
            <Input
              label="Description (Optional)"
              placeholder="What's this group for?"
              multiline
              rows={3}
              {...register('description', {
                maxLength: {
                  value: 200,
                  message: 'Description must be less than 200 characters'
                }
              })}
              error={errors.description?.message}
            />
          </div>

          <div className="modal__actions">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
            >
              Create Group
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
