import uuid
from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator


def to_major_units(minor_units_value):
    """
    Helper function to convert minor currency units (e.g., cents) to major units (e.g., dollars).

    Args:
        minor_units_value: Value in minor currency units (e.g., cents)

    Returns:
        Value in major currency units (e.g., dollars) as a decimal
    """
    if minor_units_value is None:
        return 0
    return minor_units_value / 100


class Group(models.Model):
    """
    A group for splitting expenses among members.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_groups'
    )
    telegram_chat_id = models.BigIntegerField(blank=True, null=True, db_index=True)
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this group should be visible in API responses"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Soft delete flag - group is marked as deleted but not removed to preserve expense history"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'groups'
        ordering = ['-created_at']
    def __str__(self):
        return self.name


class Membership(models.Model):
    """
    Membership relationship between users and groups.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ROLE_CHOICES = [
        ('member', 'Member'),
        ('admin', 'Admin'),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='memberships'
    )

    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='memberships'
    )
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='member')
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this membership should be visible in API responses"
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'memberships'
        unique_together = ['user', 'group']
        ordering = ['-joined_at']
    def __str__(self):
        return f"{self.user.username} in {self.group.name} ({self.role})"


class Expense(models.Model):
    """
    An expense within a group that needs to be split among participants.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
        ('AUD', 'Australian Dollar'),
        ('IRR', 'Iranian Rial'),
        # Add more currencies as needed
    ]

    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='expenses'
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_expenses'
    )
    description = models.CharField(max_length=500)
    amount = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        help_text="Amount in minor currency units (e.g., cents) to avoid floating point issues"
    )
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this expense should be visible in API responses"
    )
    occurred_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'expenses'
        ordering = ['-occurred_at', '-created_at']

    def __str__(self):
        return f"{self.description} - {self.amount_major_units:.2f} {self.currency}"

    @property
    def amount_major_units(self):
        """Convert minor units to major units for display purposes."""
        return to_major_units(self.amount)


class ExpenseParticipant(models.Model):
    """
    Tracks how much each user paid and owes for a specific expense.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    expense = models.ForeignKey(
        Expense,
        on_delete=models.CASCADE,
        related_name='participants'
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='expense_participations'
    )
    share = models.PositiveIntegerField(
        validators=[MinValueValidator(0)],
        help_text="Amount this user owes for this expense in minor currency units (e.g., cents)"
    )
    paid = models.PositiveIntegerField(
        validators=[MinValueValidator(0)],
        default=0,
        help_text="Amount this user actually paid for this expense in minor currency units (e.g., cents)"
    )
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this participation should be visible in API responses"
    )

    class Meta:
        db_table = 'expense_participants'
        unique_together = ['expense', 'user']
        ordering = ['expense', 'user']
    def __str__(self):
        return f"{self.user.username} - {self.expense.description}"

    @property
    def share_major_units(self):
        """Convert share to major units for display purposes."""
        return to_major_units(self.share)

    @property
    def paid_major_units(self):
        """Convert paid to major units for display purposes."""
        return to_major_units(self.paid)

    @property
    def net_minor_units(self):
        """Calculate net amount (paid - share) in minor units."""
        return self.paid - self.share

    @property
    def net_major_units(self):
        """Calculate net amount (paid - share) in major units."""
        return to_major_units(self.net_minor_units)


class Settlement(models.Model):
    """
    Records a settlement/payment between two users in a group.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='settlements'
    )

    payer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments_made',
        help_text="User who made the payment"
    )
    payee = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments_received',
        help_text="User who received the payment"
    )
    amount = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        help_text="Settlement amount in minor currency units (e.g., cents)"
    )
    currency = models.CharField(max_length=3, choices=Expense.CURRENCY_CHOICES, default='USD')
    note = models.TextField(blank=True, null=True)
    is_published = models.BooleanField(
        default=True,
        help_text="Whether this settlement should be visible in API responses"
    )
    settled_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'settlements'
        ordering = ['-settled_at', '-created_at']
    def __str__(self):
        return f"{self.payer.username} → {self.payee.username}: {self.amount_major_units:.2f} {self.currency}"

    @property
    def amount_major_units(self):
        """Convert minor units to major units for display purposes."""
        return to_major_units(self.amount)

    def clean(self):
        """Validate that payer and payee are different users."""
        from django.core.exceptions import ValidationError
        if self.payer == self.payee:
            raise ValidationError("Payer and payee cannot be the same user.")
