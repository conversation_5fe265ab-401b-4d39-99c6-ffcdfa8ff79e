import { forwardRef } from 'react';
import './Input.css';

/**
 * Telegram-style Input component
 */
const Input = forwardRef(function Input({
  label,
  placeholder,
  error,
  type = 'text',
  disabled = false,
  multiline = false,
  rows = 3,
  className = '',
  ...props
}, ref) {
  const baseClass = 'tg-input';
  const errorClass = error ? 'tg-input--error' : '';
  const disabledClass = disabled ? 'tg-input--disabled' : '';
  const multilineClass = multiline ? 'tg-input--multiline' : '';

  const classes = [
    baseClass,
    errorClass,
    disabledClass,
    multilineClass,
    className
  ].filter(Boolean).join(' ');

  const InputComponent = multiline ? 'textarea' : 'input';
  const inputProps = multiline
    ? { rows, placeholder, disabled, className: classes, ...props }
    : { type, placeholder, disabled, className: classes, ...props };

  return (
    <div className="tg-input-wrapper">
      {label && (
        <label className="tg-input__label">
          {label}
        </label>
      )}

      <InputComponent
        ref={ref}
        {...inputProps}
      />

      {error && (
        <div className="tg-input__error">
          {error}
        </div>
      )}
    </div>
  );
});

export default Input;
