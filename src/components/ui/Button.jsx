import './Button.css';

/**
 * Telegram-style Button component
 */
export default function Button({ 
  children, 
  variant = 'primary', 
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  className = '',
  ...props 
}) {
  const baseClass = 'tg-button';
  const variantClass = `tg-button--${variant}`;
  const sizeClass = `tg-button--${size}`;
  const disabledClass = disabled ? 'tg-button--disabled' : '';
  const loadingClass = loading ? 'tg-button--loading' : '';
  
  const classes = [
    baseClass,
    variantClass,
    sizeClass,
    disabledClass,
    loadingClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && <span className="tg-button__spinner" />}
      <span className="tg-button__content">
        {children}
      </span>
    </button>
  );
}
