import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import AuthScreen from './screens/Auth/AuthScreen';
import TelegramAuthScreen from './screens/Auth/TelegramAuthScreen';
import ResetPasswordScreen from './screens/Auth/ResetPasswordScreen';
import ChangePasswordScreen from './screens/Auth/ChangePasswordScreen';
import ProfileScreen from './screens/Profile/ProfileScreen';
import GroupListScreen from './screens/Groups/GroupListScreen';
import GroupDetailScreen from './screens/Groups/GroupDetailScreen';
import AddExpenseScreen from './screens/Expenses/AddExpenseScreen';
import ExpenseDetailScreen from './screens/Expenses/ExpenseDetailScreen';
import SettlementScreen from './screens/Settlement/SettlementScreen';
import LoadingScreen from './screens/LoadingScreen';
import './App.css';

// Protected route component
function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  return children;
}

// Root route component that handles authentication-based redirection
function RootRoute() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return <Navigate to={isAuthenticated ? "/groups" : "/auth"} replace />;
}

// Main app component
function AppContent() {
  const { isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Router>
      <Routes>
        {/* Authentication routes */}
        <Route path="/auth" element={<AuthScreen />} />
        <Route path="/telegram" element={<TelegramAuthScreen />} />
        <Route path="/reset-password" element={<ResetPasswordScreen />} />

        {/* Root route with smart redirection */}
        <Route path="/" element={<RootRoute />} />

        {/* Protected routes */}
        <Route path="/groups" element={
          <ProtectedRoute>
            <GroupListScreen />
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <ProfileScreen />
          </ProtectedRoute>
        } />
        <Route path="/change-password" element={
          <ProtectedRoute>
            <ChangePasswordScreen />
          </ProtectedRoute>
        } />
        <Route path="/groups/:groupId" element={
          <ProtectedRoute>
            <GroupDetailScreen />
          </ProtectedRoute>
        } />
        <Route path="/groups/:groupId/add-expense" element={
          <ProtectedRoute>
            <AddExpenseScreen />
          </ProtectedRoute>
        } />
        <Route path="/expenses/:expenseId" element={
          <ProtectedRoute>
            <ExpenseDetailScreen />
          </ProtectedRoute>
        } />
        <Route path="/groups/:groupId/settle" element={
          <ProtectedRoute>
            <SettlementScreen />
          </ProtectedRoute>
        } />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
