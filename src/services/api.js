/**
 * API service for communicating with the Spliton backend
 */

import axios from 'axios';
import { getTelegramWebApp } from '../utils/telegram';

// API base URL - update this to match your backend
const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // For HttpOnly cookies
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Try to refresh token
        await refreshToken();
        const token = localStorage.getItem('access_token');
        if (token) {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        window.location.href = '/auth';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

/**
 * Authentication API
 */
export const authAPI = {
  // Email/Password Authentication
  register: async (userData) => {
    const response = await api.post('/auth/register/', userData);

    // Store access token
    if (response.data.access) {
      localStorage.setItem('access_token', response.data.access);
    }

    return response.data;
  },

  login: async (credentials) => {
    const response = await api.post('/auth/login/', credentials);

    // Store access token
    if (response.data.access) {
      localStorage.setItem('access_token', response.data.access);
    }

    return response.data;
  },

  // Password Management
  requestPasswordReset: async (email) => {
    const response = await api.post('/auth/password/reset/request/', { email });
    return response.data;
  },

  confirmPasswordReset: async (resetData) => {
    const response = await api.post('/auth/password/reset/confirm/', resetData);
    return response.data;
  },

  changePassword: async (passwordData) => {
    const response = await api.post('/auth/password/change/', passwordData);
    return response.data;
  },

  // User Profile
  getProfile: async () => {
    const response = await api.get('/auth/profile/');
    return response.data;
  },

  updateProfile: async (profileData) => {
    const response = await api.put('/auth/profile/', profileData);
    return response.data;
  },

  // Account Linking
  linkProvider: async (providerData) => {
    const response = await api.post('/auth/link/', providerData);
    return response.data;
  },

  unlinkProvider: async (providerId) => {
    const response = await api.delete('/auth/unlink/', {
      data: { provider_id: providerId }
    });
    return response.data;
  },

  // Verify Telegram WebApp data
  verifyTelegramWebApp: async () => {
    const tg = getTelegramWebApp();
    const response = await api.post('/auth/telegram_webapp/verify/', {
      initData: tg.initData
    });

    // Store access token
    if (response.data.access) {
      localStorage.setItem('access_token', response.data.access);
    }

    return response.data;
  },

  // Link Telegram to existing account
  linkTelegram: async () => {
    const tg = getTelegramWebApp();
    const response = await api.post('/auth/telegram_webapp/link/', {
      initData: tg.initData
    });
    return response.data;
  },

  // Refresh access token
  refreshToken: async () => {
    const response = await api.post('/auth/token/refresh/');
    if (response.data.access) {
      localStorage.setItem('access_token', response.data.access);
    }
    return response.data;
  },

  // Logout
  logout: async () => {
    try {
      await api.post('/auth/logout/');
    } finally {
      localStorage.removeItem('access_token');
    }
  }
};

/**
 * Groups API
 */
export const groupsAPI = {
  // Get user's groups
  getGroups: async () => {
    const response = await api.get('/groups/');
    // Handle paginated response from DRF
    return response.data.results || response.data;
  },
  
  // Get group details
  getGroup: async (groupId) => {
    const response = await api.get(`/groups/${groupId}/`);
    return response.data;
  },
  
  // Create new group
  createGroup: async (groupData) => {
    const response = await api.post('/groups/', groupData);
    return response.data;
  },
  
  // Add member to group
  addMember: async (groupId, memberData) => {
    const response = await api.post(`/groups/${groupId}/add_member/`, memberData);
    return response.data;
  },
  
  // Get group balances
  getGroupBalances: async (groupId) => {
    const response = await api.get(`/groups/${groupId}/balances/`);
    return response.data;
  },

  // Get suggested transfers for settling up
  getSuggestedTransfers: async (groupId) => {
    const response = await api.get(`/groups/${groupId}/suggested_transfers/`);
    return response.data;
  }
};

/**
 * Expenses API
 */
export const expensesAPI = {
  // Get expenses (optionally filtered by group)
  getExpenses: async (groupId = null) => {
    const params = groupId ? { group: groupId } : {};
    const response = await api.get('/expenses/', { params });
    // Handle paginated response from DRF
    return response.data.results || response.data;
  },
  
  // Get expense details
  getExpense: async (expenseId) => {
    const response = await api.get(`/expenses/${expenseId}/`);
    return response.data;
  },
  
  // Create new expense
  createExpense: async (groupId, expenseData) => {
    const response = await api.post(`/groups/${groupId}/expenses/`, expenseData);
    return response.data;
  },
  
  // Update expense
  updateExpense: async (expenseId, expenseData) => {
    const response = await api.put(`/expenses/${expenseId}/`, expenseData);
    return response.data;
  },
  
  // Delete expense
  deleteExpense: async (expenseId) => {
    const response = await api.delete(`/expenses/${expenseId}/`);
    return response.data;
  }
};

/**
 * Settlements API
 */
export const settlementsAPI = {
  // Get settlements (optionally filtered by group)
  getSettlements: async (groupId = null) => {
    const params = groupId ? { group: groupId } : {};
    const response = await api.get('/settlements/', { params });
    // Handle paginated response from DRF
    return response.data.results || response.data;
  },
  
  // Create new settlement
  createSettlement: async (groupId, settlementData) => {
    const response = await api.post(`/groups/${groupId}/settlements/`, settlementData);
    return response.data;
  }
};

// Helper function for token refresh
async function refreshToken() {
  return authAPI.refreshToken();
}

export default api;
