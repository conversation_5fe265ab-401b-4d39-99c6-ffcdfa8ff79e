/**
 * Currency utility functions for handling monetary values
 * All amounts are stored in minor units (cents) to avoid floating-point issues
 */

/**
 * Convert major currency units (dollars) to minor units (cents)
 * @param {number} majorUnits - Amount in major units (e.g., 10.50)
 * @returns {number} Amount in minor units (e.g., 1050)
 */
export function toMinorUnits(majorUnits) {
  return Math.round(majorUnits * 100);
}

/**
 * Convert minor currency units (cents) to major units (dollars)
 * @param {number} minorUnits - Amount in minor units (e.g., 1050)
 * @returns {number} Amount in major units (e.g., 10.50)
 */
export function toMajorUnits(minorUnits) {
  return minorUnits / 100;
}

/**
 * Format currency amount for display
 * @param {number} minorUnits - Amount in minor units
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted currency string
 */
export function formatCurrency(minorUnits, currency = 'USD') {
  const majorUnits = toMajorUnits(minorUnits);
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(majorUnits);
}

/**
 * Format balance with positive/negative indicators
 * @param {number} balanceMinorUnits - Balance in minor units
 * @param {string} currency - Currency code
 * @returns {object} Formatted balance object
 */
export function formatBalance(balanceMinorUnits, currency = 'USD') {
  const majorUnits = toMajorUnits(Math.abs(balanceMinorUnits));
  const isPositive = balanceMinorUnits >= 0;
  
  return {
    amount: majorUnits.toFixed(2),
    isOwed: isPositive,
    isOwing: !isPositive,
    displayText: `${isPositive ? '+' : '-'}${formatCurrency(Math.abs(balanceMinorUnits), currency)}`,
    rawAmount: balanceMinorUnits
  };
}

/**
 * Parse user input to minor units
 * @param {string} input - User input string
 * @returns {number} Amount in minor units
 */
export function parseUserInput(input) {
  const cleaned = input.replace(/[^0-9.]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : toMinorUnits(parsed);
}

/**
 * Parse currency input to minor units (alias for parseUserInput)
 * @param {string} input - User input string
 * @returns {number} Amount in minor units
 */
export function parseCurrencyInput(input) {
  return parseUserInput(input);
}
