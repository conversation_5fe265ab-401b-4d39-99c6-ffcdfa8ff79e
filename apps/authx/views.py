from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
from django.http import JsonResponse
from django.utils import timezone
import logging

from .utils import verify_telegram_webapp_data, create_or_update_user_from_telegram, link_telegram_to_user
from .serializers import (
    TelegramWebAppVerifySerializer, UserSerializer,
    UserRegistrationSerializer, UserLoginSerializer,
    LinkAccountSerializer, UnlinkAccountSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    ChangePasswordSerializer, UserProfileUpdateSerializer
)

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def telegram_webapp_verify(request):
    """
    Verify Telegram WebApp initData and issue JWT tokens.

    Expected payload:
    {
        "initData": "user=%7B...%7D&auth_date=...&hash=..."
    }

    Returns:
    {
        "user": {...},
        "access": "jwt_access_token",
        "refresh": "jwt_refresh_token"
    }

    Also sets HttpOnly cookies for access and refresh tokens.
    """
    serializer = TelegramWebAppVerifySerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid request data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    init_data = serializer.validated_data['initData']

    # Verify Telegram data
    telegram_data = verify_telegram_webapp_data(init_data)
    if not telegram_data:
        logger.warning(f"Failed Telegram WebApp verification from IP: {request.META.get('REMOTE_ADDR')}")
        return Response(
            {'error': 'Invalid Telegram WebApp data'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Create or update user
        user, created = create_or_update_user_from_telegram(telegram_data)

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Prepare response data
        response_data = {
            'user': UserSerializer(user).data,
            'access': str(access_token),
            'refresh': str(refresh),
            'created': created,
        }

        # Create response
        response = Response(response_data, status=status.HTTP_200_OK)

        # Set HttpOnly cookies
        set_auth_cookies(response, access_token, refresh)

        logger.info(f"Successful Telegram WebApp authentication for user: {user.username} (created: {created})")
        return response

    except Exception as e:
        logger.error(f"Error during Telegram WebApp authentication: {str(e)}")
        return Response(
            {'error': 'Authentication failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def set_auth_cookies(response, access_token, refresh_token):
    """
    Helper function to set authentication cookies on response.
    """
    response.set_cookie(
        'access',
        str(access_token),
        max_age=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
        httponly=True,
        secure=settings.SECURE_COOKIE_SECURE,
        samesite=settings.SECURE_COOKIE_SAMESITE,
    )

    response.set_cookie(
        'refresh',
        str(refresh_token),
        max_age=settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds(),
        httponly=True,
        secure=settings.SECURE_COOKIE_SECURE,
        samesite=settings.SECURE_COOKIE_SAMESITE,
    )


@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """
    Register a new user with email and password.

    Expected payload:
    {
        "email": "<EMAIL>",
        "password": "secure_password",
        "first_name": "John",  // optional
        "last_name": "Doe"     // optional
    }

    Returns:
    {
        "user": {...},
        "access": "jwt_access_token",
        "refresh": "jwt_refresh_token",
        "message": "Registration successful. Please check your email to verify your account."
    }

    Also sets HttpOnly cookies for access and refresh tokens.
    """
    serializer = UserRegistrationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid registration data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Create user
        user = serializer.save()

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Prepare response data
        response_data = {
            'user': UserSerializer(user).data,
            'access': str(access_token),
            'refresh': str(refresh),
            'message': 'Registration successful. Please check your email to verify your account.'
        }

        # Create response
        response = Response(response_data, status=status.HTTP_201_CREATED)

        # Set HttpOnly cookies
        set_auth_cookies(response, access_token, refresh)

        logger.info(f"Successful user registration: {user.email}")
        return response

    except Exception as e:
        logger.error(f"Error during user registration: {str(e)}")
        return Response(
            {'error': 'Registration failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """
    Login user with email and password.

    Expected payload:
    {
        "email": "<EMAIL>",
        "password": "secure_password"
    }

    Returns:
    {
        "user": {...},
        "access": "jwt_access_token",
        "refresh": "jwt_refresh_token"
    }

    Also sets HttpOnly cookies for access and refresh tokens.
    """
    serializer = UserLoginSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid login data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user = serializer.validated_data['user']

        # Update last_used_at for email authentication provider
        from apps.accounts.models import AuthenticationProvider
        try:
            auth_provider = AuthenticationProvider.objects.get(
                user=user,
                provider='email'
            )
            auth_provider.save()  # This will update last_used_at due to auto_now=True
        except AuthenticationProvider.DoesNotExist:
            logger.warning(f"Email auth provider not found for user: {user.email}")

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Prepare response data
        response_data = {
            'user': UserSerializer(user).data,
            'access': str(access_token),
            'refresh': str(refresh),
        }

        # Create response
        response = Response(response_data, status=status.HTTP_200_OK)

        # Set HttpOnly cookies
        set_auth_cookies(response, access_token, refresh)

        logger.info(f"Successful user login: {user.email}")
        return response

    except Exception as e:
        logger.error(f"Error during user login: {str(e)}")
        return Response(
            {'error': 'Login failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def link_account(request):
    """
    Link an authentication provider to the current user's account.

    Expected payload:
    {
        "provider": "google",
        "provider_user_id": "google_user_id",
        "provider_username": "username",  // optional
        "provider_email": "<EMAIL>",  // optional
        "provider_data": {...}  // optional additional data
    }

    Returns:
    {
        "message": "Account linked successfully",
        "provider": {...}
    }
    """
    serializer = LinkAccountSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid link data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        from apps.accounts.models import AuthenticationProvider

        # Create the authentication provider
        auth_provider = AuthenticationProvider.objects.create(
            user=request.user,
            provider=serializer.validated_data['provider'],
            provider_user_id=serializer.validated_data['provider_user_id'],
            provider_username=serializer.validated_data.get('provider_username', ''),
            provider_email=serializer.validated_data.get('provider_email', ''),
            provider_data=serializer.validated_data.get('provider_data', {}),
            is_verified=True,  # Assume verified if we're linking
        )

        response_data = {
            'message': 'Account linked successfully',
            'provider': {
                'id': str(auth_provider.id),
                'provider': auth_provider.provider,
                'provider_username': auth_provider.provider_username,
                'provider_email': auth_provider.provider_email,
                'is_primary': auth_provider.is_primary,
                'is_verified': auth_provider.is_verified,
                'linked_at': auth_provider.linked_at,
            }
        }

        logger.info(f"Successfully linked {auth_provider.provider} account for user: {request.user.email}")
        return Response(response_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error during account linking: {str(e)}")
        return Response(
            {'error': 'Account linking failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def unlink_account(request):
    """
    Unlink an authentication provider from the current user's account.

    Expected payload:
    {
        "provider_id": "uuid-of-provider-to-unlink"
    }

    Returns:
    {
        "message": "Account unlinked successfully"
    }
    """
    serializer = UnlinkAccountSerializer(data=request.data, context={'request': request})
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid unlink data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        from apps.accounts.models import AuthenticationProvider

        provider_id = serializer.validated_data['provider_id']
        auth_provider = AuthenticationProvider.objects.get(
            id=provider_id,
            user=request.user
        )

        provider_type = auth_provider.provider
        auth_provider.delete()

        logger.info(f"Successfully unlinked {provider_type} account for user: {request.user.email}")
        return Response(
            {'message': 'Account unlinked successfully'},
            status=status.HTTP_200_OK
        )

    except Exception as e:
        logger.error(f"Error during account unlinking: {str(e)}")
        return Response(
            {'error': 'Account unlinking failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def profile(request):
    """
    Get current user's profile with all linked authentication providers.

    Returns:
    {
        "user": {...}
    }
    """
    try:
        user_data = UserSerializer(request.user).data
        return Response({'user': user_data}, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error fetching user profile: {str(e)}")
        return Response(
            {'error': 'Failed to fetch profile'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_profile(request):
    """
    Update current user's profile information.

    Allows partial updates of username, first_name, last_name, and profile_photo.
    Any field provided in the request will be updated.

    Expected payload (all fields optional):
    {
        "username": "new_username",
        "first_name": "John",
        "last_name": "Doe",
        "profile_photo": "https://example.com/photo.jpg"
    }

    Returns:
    {
        "user": {...},
        "message": "Profile updated successfully"
    }
    """
    try:
        serializer = UserProfileUpdateSerializer(
            request.user,
            data=request.data,
            partial=True
        )

        if not serializer.is_valid():
            return Response(
                {'error': 'Invalid profile data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Save the updated user
        user = serializer.save()

        # Return updated user data
        response_data = {
            'user': UserSerializer(user).data,
            'message': 'Profile updated successfully'
        }

        logger.info(f"Profile updated for user: {user.email or user.username}")
        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        return Response(
            {'error': 'Failed to update profile'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def link_telegram(request):
    """
    Link Telegram account to the current user's account.

    Expected payload:
    {
        "initData": "user=%7B...%7D&auth_date=...&hash=..."
    }

    Returns:
    {
        "message": "Telegram account linked successfully",
        "telegram_profile": {...}
    }
    """
    serializer = TelegramWebAppVerifySerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid request data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    init_data = serializer.validated_data['initData']

    # Verify Telegram data
    telegram_data = verify_telegram_webapp_data(init_data)
    if not telegram_data:
        logger.warning(f"Failed Telegram WebApp verification from IP: {request.META.get('REMOTE_ADDR')}")
        return Response(
            {'error': 'Invalid Telegram WebApp data'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Link Telegram account to current user
        telegram_profile = link_telegram_to_user(request.user, telegram_data)

        from .serializers import TelegramProfileSerializer
        response_data = {
            'message': 'Telegram account linked successfully',
            'telegram_profile': TelegramProfileSerializer(telegram_profile).data
        }

        logger.info(f"Successfully linked Telegram account for user: {request.user.email}")
        return Response(response_data, status=status.HTTP_201_CREATED)

    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Error during Telegram account linking: {str(e)}")
        return Response(
            {'error': 'Telegram account linking failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_request(request):
    """
    Request a password reset token.

    Expected payload:
    {
        "email": "<EMAIL>"
    }

    Returns:
    {
        "message": "If an account with this email exists, a password reset link has been sent."
    }
    """
    serializer = PasswordResetRequestSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid request data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    email = serializer.validated_data['email']

    try:
        from apps.accounts.models import User, PasswordResetToken

        # Try to find user by email
        try:
            user = User.objects.get(email=email)

            # Invalidate any existing tokens for this user
            PasswordResetToken.objects.filter(
                user=user,
                used_at__isnull=True
            ).update(used_at=timezone.now())

            # Create new password reset token
            reset_token = PasswordResetToken.objects.create(user=user)

            # In a real application, you would send an email here
            # For now, we'll just log the token (DO NOT DO THIS IN PRODUCTION)
            logger.info(f"Password reset token for {email}: {reset_token.token}")

            # TODO: Send email with reset link
            # send_password_reset_email(user, reset_token.token)

        except User.DoesNotExist:
            # Don't reveal whether the email exists or not
            logger.info(f"Password reset requested for non-existent email: {email}")

        # Always return the same message for security
        return Response({
            'message': 'If an account with this email exists, a password reset link has been sent.'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error during password reset request: {str(e)}")
        return Response(
            {'error': 'Password reset request failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_confirm(request):
    """
    Confirm password reset with token.

    Expected payload:
    {
        "email": "<EMAIL>",
        "token": "reset_token",
        "new_password": "new_secure_password"
    }

    Returns:
    {
        "message": "Password has been reset successfully."
    }
    """
    serializer = PasswordResetConfirmSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid request data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        from apps.accounts.models import User, PasswordResetToken

        email = serializer.validated_data['email']
        token = serializer.validated_data['token']
        new_password = serializer.validated_data['new_password']

        # Find user and token
        try:
            user = User.objects.get(email=email)
            reset_token = PasswordResetToken.objects.get(
                user=user,
                token=token
            )
        except (User.DoesNotExist, PasswordResetToken.DoesNotExist):
            return Response(
                {'error': 'Invalid reset token or email.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if token is valid
        if not reset_token.is_valid():
            return Response(
                {'error': 'Reset token has expired or been used.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Reset password
        user.set_password(new_password)
        user.save()

        # Mark token as used
        reset_token.mark_as_used()

        logger.info(f"Password reset successful for user: {email}")
        return Response({
            'message': 'Password has been reset successfully.'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error during password reset confirmation: {str(e)}")
        return Response(
            {'error': 'Password reset failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password(request):
    """
    Change password for authenticated user.

    Expected payload:
    {
        "current_password": "current_password",
        "new_password": "new_secure_password"
    }

    Returns:
    {
        "message": "Password changed successfully."
    }
    """
    serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
    if not serializer.is_valid():
        return Response(
            {'error': 'Invalid request data', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        new_password = serializer.validated_data['new_password']

        # Change password
        request.user.set_password(new_password)
        request.user.save()

        logger.info(f"Password changed for user: {request.user.email}")
        return Response({
            'message': 'Password changed successfully.'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error during password change: {str(e)}")
        return Response(
            {'error': 'Password change failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
