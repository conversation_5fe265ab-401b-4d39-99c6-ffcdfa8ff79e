# Spliton Backend - MLP

A Django REST API backend for <PERSON>on, a Telegram Mini App for splitting expenses among friends. This is the **Minimum Lovable Product (MLP)** implementation.

## Features

### Authentication & Account Management
- 🔐 **Multi-Provider Authentication** - Support for email/password and Telegram WebApp authentication
- 🔗 **Account Linking** - Link multiple authentication providers to a single account
- 🔑 **Password Management** - Password reset via email and password change for authenticated users
- 🛡️ **Secure Token System** - JWT tokens with HttpOnly cookies and HMAC-SHA256 verification for Telegram
- 👤 **User Profiles** - Comprehensive user profile management with linked authentication providers

### Core Functionality
- 👥 **Group Management** - Create and manage expense-splitting groups
- 💰 **Expense Tracking** - Add expenses with flexible participant splitting
- ⚖️ **Balance Calculations** - Automatic calculation of who owes what
- 🔄 **Smart Transfer Suggestions** - Optimized settlement recommendations
- 💳 **Settlement Recording** - Track payments between group members
- 🤖 **Telegram Bot Integration** - Minimal webhook for launching the Mini App

### Security & Architecture
- 🔒 **UUID Security** - All models use UUID primary keys for enhanced security
- 🌍 **Currency-Neutral Design** - Simplified field naming that works with any currency
- 🗑️ **Soft Deletion** - Safe data removal with preservation for integrity
- 👁️ **Publication Control** - Fine-grained control over API data visibility
- 🔐 **Account Merging Ready** - Architecture supports future OAuth providers (Google, GitHub, Apple)

## Tech Stack

- **Backend**: Django 5.2 + Django REST Framework
- **Authentication**: Multi-provider (Email/Password + Telegram WebApp) with JWT and HttpOnly cookies
- **Database**: PostgreSQL with UUID primary keys
- **API**: RESTful API with comprehensive authentication, permissions, and validation
- **Testing**: Comprehensive test suite with 58+ tests covering all authentication flows

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL
- Telegram Bot Token

### Installation

1. **Clone and setup environment**:
```bash
git clone <repository-url>
cd spliton-backend
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Setup database**:
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

4. **Run development server**:
```bash
python manage.py runserver 0.0.0.0:8000
```

## Environment Variables

Create a `.env` file in the project root:

```env
# Django Configuration
DJANGO_DEBUG=True
DJANGO_SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=postgresql://spliton_user:spliton@localhost:5432/spliton

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=123456:ABC-DEF-your-bot-token-here

# CORS and CSRF Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://localhost:5173
CSRF_TRUSTED_ORIGINS=http://localhost:3000,https://localhost:5173

# Telegram Web App URL (for bot buttons)
TWA_URL=https://your-domain.com/webapp

# JWT Configuration (optional)
JWT_ACCESS_TOKEN_LIFETIME_MINUTES=20
JWT_REFRESH_TOKEN_LIFETIME_DAYS=14
```

## API Endpoints

### Important Notes

- **UUIDs**: All resource IDs are UUIDs (e.g., `550e8400-e29b-41d4-a716-************`) instead of integers
- **Currency Fields**: Monetary amounts use simplified names (`amount`, `share`, `paid`) and are stored in minor currency units (e.g., cents)
- **Display Values**: All monetary fields have corresponding `_major_units` properties for display (e.g., `amount_major_units`)
- **Soft Deletion**: Resources have `is_deleted` and `is_published` flags for data management
- **Security**: UUID-based URLs prevent ID enumeration attacks

### Authentication

The API supports multiple authentication methods that can be linked to a single user account:

#### Email/Password Registration
```http
POST /api/auth/register/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Response**: User info + JWT tokens + HttpOnly cookies

#### Email/Password Login
```http
POST /api/auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response**: User info + JWT tokens + HttpOnly cookies

#### Get User Profile
```http
GET /api/auth/profile/
Authorization: Bearer <access_token>
```

**Response**: User info with all linked authentication providers

#### Password Reset Request
```http
POST /api/auth/password/reset/request/
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response**: Success message (same response regardless of email existence for security)

#### Password Reset Confirmation
```http
POST /api/auth/password/reset/confirm/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "token": "reset_token_from_email",
  "new_password": "NewSecurePassword123!",
  "new_password_confirm": "NewSecurePassword123!"
}
```

**Response**: Success message

#### Change Password (Authenticated)
```http
POST /api/auth/password/change/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "current_password": "CurrentPassword123!",
  "new_password": "NewSecurePassword123!",
  "new_password_confirm": "NewSecurePassword123!"
}
```

**Response**: Success message

#### Link Authentication Provider
```http
POST /api/auth/link/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "provider": "google",
  "provider_user_id": "google_user_123",
  "provider_username": "john.doe",
  "provider_email": "<EMAIL>",
  "provider_data": {
    "name": "John Doe",
    "picture": "https://example.com/avatar.jpg"
  }
}
```

**Response**: Success message with provider details

#### Unlink Authentication Provider
```http
DELETE /api/auth/unlink/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "provider_id": "uuid-of-provider-to-unlink"
}
```

**Response**: Success message

#### Verify Telegram WebApp
```http
POST /api/auth/telegram_webapp/verify/
Content-Type: application/json

{
  "initData": "user=%7B...%7D&auth_date=...&hash=..."
}
```

**Response**: User info + JWT tokens + HttpOnly cookies

#### Link Telegram to Existing Account
```http
POST /api/auth/telegram_webapp/link/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "initData": "user=%7B...%7D&auth_date=...&hash=..."
}
```

**Response**: Success message with Telegram profile details

### Groups

#### List Groups
```http
GET /api/groups/
Authorization: Bearer <access_token>
```

#### Create Group
```http
POST /api/groups/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "Roommates"
}
```

#### Add Member to Group
```http
POST /api/groups/{group_uuid}/add_member/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "role": "member"
}
```

#### Get Group Balances
```http
GET /api/groups/{group_uuid}/balances/
Authorization: Bearer <access_token>
```

**Response**:
```json
{
  "balances": {
    "550e8400-e29b-41d4-a716-************": 2000,
    "550e8400-e29b-41d4-a716-************": -1000,
    "550e8400-e29b-41d4-a716-************": -1000
  },
  "suggested_transfers": [
    ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************", 1000],
    ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************", 1000]
  ]
}
```

### Expenses

#### List Expenses
```http
GET /api/expenses/
Authorization: Bearer <access_token>
```

#### Create Expense
```http
POST /api/expenses/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "group": "550e8400-e29b-41d4-a716-************",
  "description": "Dinner at restaurant",
  "amount": 6000,
  "currency": "USD",
  "occurred_at": "2025-09-20T18:00:00Z",
  "participants": [
    {
      "user_id": "550e8400-e29b-41d4-a716-************",
      "share": 3000,
      "paid": 6000
    },
    {
      "user_id": "550e8400-e29b-41d4-a716-************",
      "share": 3000,
      "paid": 0
    }
  ]
}
```

**Response**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440010",
  "group": "550e8400-e29b-41d4-a716-************",
  "group_name": "Roommates",
  "created_by": "550e8400-e29b-41d4-a716-************",
  "created_by_username": "john_doe",
  "description": "Dinner at restaurant",
  "amount": 6000,
  "amount_major_units": "60.00",
  "currency": "USD",
  "occurred_at": "2025-09-20T18:00:00Z",
  "created_at": "2025-09-22T01:00:00Z",
  "updated_at": "2025-09-22T01:00:00Z",
  "participants": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440020",
      "user_id": "550e8400-e29b-41d4-a716-************",
      "username": "john_doe",
      "share": 3000,
      "paid": 6000,
      "share_major_units": "30.00",
      "paid_major_units": "60.00",
      "net_major_units": "30.00"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440021",
      "user_id": "550e8400-e29b-41d4-a716-************",
      "username": "jane_doe",
      "share": 3000,
      "paid": 0,
      "share_major_units": "30.00",
      "paid_major_units": "0.00",
      "net_major_units": "-30.00"
    }
  ]
}
```

### Settlements

#### List Settlements
```http
GET /api/settlements/
Authorization: Bearer <access_token>
```

#### Create Settlement
```http
POST /api/settlements/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "group": "550e8400-e29b-41d4-a716-************",
  "payer_id": "550e8400-e29b-41d4-a716-************",
  "payee_id": "550e8400-e29b-41d4-a716-************",
  "amount": 3000,
  "currency": "USD",
  "settled_at": "2025-09-20T19:00:00Z",
  "note": "Cash payment"
}
```

**Response**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440030",
  "group": "550e8400-e29b-41d4-a716-************",
  "group_name": "Roommates",
  "payer": "550e8400-e29b-41d4-a716-************",
  "payer_username": "jane_doe",
  "payee": "550e8400-e29b-41d4-a716-************",
  "payee_username": "john_doe",
  "amount": 3000,
  "amount_major_units": "30.00",
  "currency": "USD",
  "note": "Cash payment",
  "settled_at": "2025-09-20T19:00:00Z",
  "created_at": "2025-09-22T01:00:00Z"
}
```

## Telegram Bot Setup

1. **Set webhook URL**:
```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://your-domain.com/bot/webhook/"}'
```

2. **Test the bot**:
   - Send `/start` to your bot
   - It should reply with a button to open the Mini App

## Testing

The project includes a comprehensive test suite with 58+ tests covering all authentication flows, account linking, password management, and core functionality.

### Run All Tests
```bash
# Run all tests
python manage.py test

# Run with verbose output
python manage.py test -v 2

# Run with SQLite (to avoid PostgreSQL permissions)
DATABASE_URL=sqlite:///test_db.sqlite3 python manage.py test
```

### Run Specific Test Suites
```bash
# Authentication tests
python manage.py test apps.authx.test_authentication

# Account linking tests
python manage.py test apps.authx.test_account_linking

# Password reset tests
python manage.py test apps.authx.test_password_reset

# Model tests
python manage.py test apps.accounts.test_models

# Expense and balance tests
python manage.py test apps.expenses.tests
```

### Test Coverage
- **Email/Password Authentication**: Registration, login, validation
- **Account Linking**: Link/unlink providers, security checks
- **Password Management**: Reset tokens, password changes
- **Telegram Integration**: WebApp verification, account linking
- **Model Validation**: UUID generation, constraints, business logic
- **API Security**: Authentication, authorization, input validation

## Project Structure

```
spliton-backend/
├── manage.py
├── requirements.txt
├── .env.example
├── spliton_backend/          # Django settings and main URLs
├── apps/
│   ├── accounts/            # Custom User model and TelegramProfile
│   ├── authx/               # Telegram WebApp authentication
│   ├── expenses/            # Groups, Expenses, Settlements, Balances
│   └── bot/                 # Telegram bot webhook
└── README.md
```

## Key Features Explained

### Authentication Architecture

The authentication system is designed to support multiple authentication providers linked to a single user account, enabling future account merging capabilities:

#### Multi-Provider Support
- **Email/Password**: Traditional authentication with secure password handling
- **Telegram WebApp**: HMAC-verified authentication via Telegram Mini Apps
- **Future OAuth**: Architecture ready for Google, GitHub, Apple ID, etc.

#### Account Linking Flow
1. **Primary Registration**: User registers with email/password or Telegram
2. **Additional Providers**: User can link additional authentication methods
3. **Unified Profile**: Single user account with multiple authentication options
4. **Secure Unlinking**: Cannot remove the last authentication method

#### Security Features
- **Provider Isolation**: Each authentication method is tracked separately
- **Unique Constraints**: Prevents duplicate provider linkages
- **Token Management**: Secure password reset with time-limited tokens
- **Session Security**: JWT tokens with HttpOnly cookies and proper rotation

### Balance Calculation
- **Net Balance**: `sum(paid) - sum(share)` per user in minor currency units
- **Settlements**: Adjust balances when payments are made
- **Transfer Optimization**: Greedy algorithm to minimize transactions

### Security
- **Multi-Provider Authentication**: Support for email/password and Telegram WebApp authentication
- **Account Linking Architecture**: Secure linking of multiple authentication providers to single accounts
- **UUID Primary Keys**: All models use UUIDs instead of auto-incrementing IDs for better security
- **HMAC Verification**: All Telegram data verified with bot token using HMAC-SHA256
- **JWT Tokens**: Secure authentication with HttpOnly cookies and proper token rotation
- **Password Security**: Django's built-in password validation and secure password reset tokens
- **Account Protection**: Cannot unlink the last authentication method from an account
- **Permissions**: Group-based access control with proper authorization checks
- **Soft Deletion**: Models support soft deletion with `is_deleted` flag
- **Publication Control**: Models have `is_published` flag to control API visibility
- **Security Headers**: Proper CORS, CSRF, and cookie security settings

### Data Model
- **Users**: Extended Django User with multi-provider authentication support (UUID-based)
- **Authentication Providers**: Track different authentication methods linked to users
- **Telegram Profiles**: Detailed Telegram-specific profile information (UUID-based)
- **Password Reset Tokens**: Secure, time-limited tokens for password reset (UUID-based)
- **Groups**: Expense-splitting groups with memberships (UUID-based)
- **Expenses**: Flexible participant-based expense splitting (UUID-based)
- **Settlements**: Record of payments between users (UUID-based)

## Development Notes

- **UUID Security**: All models use UUID primary keys for better security and to prevent ID enumeration attacks
- **Currency-Neutral Fields**: Monetary fields use simplified names (`amount`, `share`, `paid`) instead of currency-specific names
- **Minor Currency Units**: All monetary amounts stored in minor currency units (e.g., cents) to avoid floating-point issues
- **Display Properties**: Helper properties with `_major_units` suffix for display purposes (e.g., `amount_major_units`)
- **Soft Deletion**: All models support soft deletion to preserve data integrity
- **Publication Control**: All models have publication flags for API visibility control
- **Comprehensive Validation**: Expense participant shares validation and business logic
- **Optimized Queries**: Database queries optimized with select_related/prefetch_related
- **Proper Error Handling**: Comprehensive error handling and logging
