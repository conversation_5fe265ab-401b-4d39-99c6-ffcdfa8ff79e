# Spliton Backend API Documentation

## Authentication Endpoints

All authentication endpoints return JWT tokens in both the response body and as HttpOnly cookies.

### Email/Password Authentication

#### Register New User
```http
POST /api/auth/register/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "password_confirm": "SecurePassword123!",
  "first_name": "<PERSON>",      // optional
  "last_name": "<PERSON><PERSON>"         // optional
}
```

**Success Response (201):**
```json
{
  "user": {
    "id": "uuid",
    "username": "user",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    "auth_providers": [
      {
        "id": "uuid",
        "provider": "email",
        "provider_email": "<EMAIL>",
        "is_primary": true,
        "is_verified": false
      }
    ]
  },
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "message": "Registration successful. Please check your email to verify your account."
}
```

#### Login User
```http
POST /api/auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Success Response (200):**
```json
{
  "user": { /* user object */ },
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token"
}
```

### Password Management

#### Request Password Reset
```http
POST /api/auth/password/reset/request/
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "message": "If an account with this email exists, a password reset link has been sent."
}
```

#### Confirm Password Reset
```http
POST /api/auth/password/reset/confirm/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "token": "reset_token_from_email",
  "new_password": "NewSecurePassword123!",
  "new_password_confirm": "NewSecurePassword123!"
}
```

**Success Response (200):**
```json
{
  "message": "Password has been reset successfully."
}
```

#### Change Password (Authenticated)
```http
POST /api/auth/password/change/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "current_password": "CurrentPassword123!",
  "new_password": "NewSecurePassword123!",
  "new_password_confirm": "NewSecurePassword123!"
}
```

**Success Response (200):**
```json
{
  "message": "Password changed successfully."
}
```

### User Profile

#### Get User Profile
```http
GET /api/auth/profile/
Authorization: Bearer <access_token>
```

**Success Response (200):**
```json
{
  "user": {
    "id": "uuid",
    "username": "user",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "date_joined": "2025-09-23T01:58:31.080452Z",
    "auth_providers": [
      {
        "id": "uuid",
        "provider": "email",
        "provider_email": "<EMAIL>",
        "is_primary": true,
        "is_verified": false
      },
      {
        "id": "uuid",
        "provider": "google",
        "provider_username": "john.doe",
        "provider_email": "<EMAIL>",
        "is_primary": false,
        "is_verified": true
      }
    ],
    "telegram_profile": null
  }
}
```

### Account Linking

#### Link Authentication Provider
```http
POST /api/auth/link/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "provider": "google",
  "provider_user_id": "google_user_123",
  "provider_username": "john.doe",        // optional
  "provider_email": "<EMAIL>",     // optional
  "provider_data": {                      // optional
    "name": "John Doe",
    "picture": "https://example.com/avatar.jpg"
  }
}
```

**Success Response (201):**
```json
{
  "message": "Account linked successfully",
  "provider": {
    "id": "uuid",
    "provider": "google",
    "provider_username": "john.doe",
    "provider_email": "<EMAIL>",
    "is_primary": false,
    "is_verified": true,
    "linked_at": "2025-09-23T02:09:54.541388Z"
  }
}
```

#### Unlink Authentication Provider
```http
DELETE /api/auth/unlink/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "provider_id": "uuid-of-provider-to-unlink"
}
```

**Success Response (200):**
```json
{
  "message": "Account unlinked successfully"
}
```

### Telegram Authentication

#### Verify Telegram WebApp
```http
POST /api/auth/telegram_webapp/verify/
Content-Type: application/json

{
  "initData": "user=%7B...%7D&auth_date=...&hash=..."
}
```

**Success Response (200):**
```json
{
  "user": { /* user object with telegram_profile */ },
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "created": true
}
```

#### Link Telegram to Existing Account
```http
POST /api/auth/telegram_webapp/link/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "initData": "user=%7B...%7D&auth_date=...&hash=..."
}
```

**Success Response (201):**
```json
{
  "message": "Telegram account linked successfully",
  "telegram_profile": {
    "telegram_user_id": *********,
    "username": "john_doe",
    "first_name": "John",
    "last_name": "Doe",
    "language_code": "en"
  }
}
```

## Error Responses

All endpoints return consistent error responses:

**Validation Error (400):**
```json
{
  "error": "Invalid request data",
  "details": {
    "field_name": ["Error message"]
  }
}
```

**Authentication Error (401):**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

**Server Error (500):**
```json
{
  "error": "Internal server error message"
}
```

## Authentication Flow Examples

### New User Registration
1. `POST /api/auth/register/` - Register with email/password
2. User receives JWT tokens and can access protected endpoints
3. Optional: Link additional providers via `POST /api/auth/link/`

### Existing User Login
1. `POST /api/auth/login/` - Login with email/password
2. User receives JWT tokens
3. `GET /api/auth/profile/` - View profile with all linked providers

### Password Reset
1. `POST /api/auth/password/reset/request/` - Request reset token
2. User receives email with reset token
3. `POST /api/auth/password/reset/confirm/` - Reset password with token

### Account Linking
1. User logs in with primary method
2. `POST /api/auth/link/` - Link additional authentication provider
3. `GET /api/auth/profile/` - Verify provider was linked
4. Optional: `DELETE /api/auth/unlink/` - Remove provider (if not last one)

## Security Notes

- All monetary amounts are in minor currency units (e.g., cents)
- All resource IDs are UUIDs for security
- JWT tokens are set as HttpOnly cookies for security
- Password reset tokens expire after 1 hour
- Cannot unlink the last authentication method from an account
- Telegram data is verified using HMAC-SHA256 with bot token
