"""
Tests for account linking functionality.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.accounts.models import AuthenticationProvider
import json

User = get_user_model()


class AccountLinkingTest(APITestCase):
    """Test account linking and unlinking functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.register_url = reverse('authx:register')
        self.login_url = reverse('authx:login')
        self.link_url = reverse('authx:link_account')
        self.unlink_url = reverse('authx:unlink_account')
        self.profile_url = reverse('authx:profile')
        
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'password_confirm': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        # Create and authenticate user
        self.client.post(self.register_url, self.user_data, format='json')
        login_response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }, format='json')
        
        self.access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        self.user = User.objects.get(email='<EMAIL>')
    
    def test_link_google_account_success(self):
        """Test successfully linking a Google account."""
        link_data = {
            'provider': 'google',
            'provider_user_id': 'google_123456789',
            'provider_username': 'testuser',
            'provider_email': '<EMAIL>',
            'provider_data': {
                'name': 'Test User',
                'picture': 'https://example.com/avatar.jpg'
            }
        }
        
        response = self.client.post(self.link_url, link_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('provider', response.data)
        
        # Check that provider was created
        auth_provider = AuthenticationProvider.objects.get(
            user=self.user,
            provider='google'
        )
        self.assertEqual(auth_provider.provider_user_id, 'google_123456789')
        self.assertEqual(auth_provider.provider_username, 'testuser')
        self.assertEqual(auth_provider.provider_email, '<EMAIL>')
        self.assertTrue(auth_provider.is_verified)
        self.assertFalse(auth_provider.is_primary)
    
    def test_link_duplicate_provider_fails(self):
        """Test that linking the same provider twice fails."""
        link_data = {
            'provider': 'google',
            'provider_user_id': 'google_123456789',
            'provider_username': 'testuser',
            'provider_email': '<EMAIL>'
        }
        
        # Link first time - should succeed
        response1 = self.client.post(self.link_url, link_data, format='json')
        self.assertEqual(response1.status_code, status.HTTP_201_CREATED)
        
        # Link second time - should fail
        response2 = self.client.post(self.link_url, link_data, format='json')
        self.assertEqual(response2.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider', response2.data['details'])
    
    def test_link_account_unauthenticated_fails(self):
        """Test that linking account without authentication fails."""
        self.client.credentials()  # Remove authentication
        
        link_data = {
            'provider': 'google',
            'provider_user_id': 'google_123456789'
        }
        
        response = self.client.post(self.link_url, link_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_unlink_account_success(self):
        """Test successfully unlinking an account."""
        # First link a Google account
        link_data = {
            'provider': 'google',
            'provider_user_id': 'google_123456789',
            'provider_username': 'testuser'
        }
        self.client.post(self.link_url, link_data, format='json')
        
        # Get the provider ID
        auth_provider = AuthenticationProvider.objects.get(
            user=self.user,
            provider='google'
        )
        
        # Unlink the account
        unlink_data = {
            'provider_id': str(auth_provider.id)
        }
        
        response = self.client.delete(self.unlink_url, unlink_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Check that provider was deleted
        self.assertFalse(
            AuthenticationProvider.objects.filter(
                user=self.user,
                provider='google'
            ).exists()
        )
    
    def test_unlink_last_provider_fails(self):
        """Test that unlinking the last authentication provider fails."""
        # Get the email provider ID
        auth_provider = AuthenticationProvider.objects.get(
            user=self.user,
            provider='email'
        )
        
        # Try to unlink the only provider
        unlink_data = {
            'provider_id': str(auth_provider.id)
        }
        
        response = self.client.delete(self.unlink_url, unlink_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider_id', response.data['details'])
        self.assertIn('last authentication method', str(response.data['details']['provider_id']))
    
    def test_unlink_nonexistent_provider_fails(self):
        """Test that unlinking a non-existent provider fails."""
        unlink_data = {
            'provider_id': '00000000-0000-0000-0000-000000000000'
        }
        
        response = self.client.delete(self.unlink_url, unlink_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider_id', response.data['details'])
    
    def test_unlink_other_users_provider_fails(self):
        """Test that unlinking another user's provider fails."""
        # Create another user
        other_user_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'password_confirm': 'TestPassword123!',
            'first_name': 'Other',
            'last_name': 'User'
        }
        self.client.credentials()  # Remove auth temporarily
        self.client.post(self.register_url, other_user_data, format='json')
        other_user = User.objects.get(email='<EMAIL>')
        
        # Get other user's email provider
        other_provider = AuthenticationProvider.objects.get(
            user=other_user,
            provider='email'
        )
        
        # Try to unlink other user's provider with our auth
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        unlink_data = {
            'provider_id': str(other_provider.id)
        }
        
        response = self.client.delete(self.unlink_url, unlink_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider_id', response.data['details'])
    
    def test_profile_shows_multiple_providers(self):
        """Test that profile endpoint shows all linked providers."""
        # Link a Google account
        link_data = {
            'provider': 'google',
            'provider_user_id': 'google_123456789',
            'provider_username': 'testuser'
        }
        self.client.post(self.link_url, link_data, format='json')
        
        # Get profile
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        auth_providers = response.data['user']['auth_providers']
        
        self.assertEqual(len(auth_providers), 2)
        
        # Check that both email and google providers are present
        provider_types = [p['provider'] for p in auth_providers]
        self.assertIn('email', provider_types)
        self.assertIn('google', provider_types)
        
        # Check that email provider is primary
        email_provider = next(p for p in auth_providers if p['provider'] == 'email')
        google_provider = next(p for p in auth_providers if p['provider'] == 'google')
        
        self.assertTrue(email_provider['is_primary'])
        self.assertFalse(google_provider['is_primary'])
    
    def test_link_invalid_provider_fails(self):
        """Test that linking an invalid provider fails."""
        link_data = {
            'provider': 'invalid_provider',
            'provider_user_id': 'user123'
        }
        
        response = self.client.post(self.link_url, link_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider', response.data['details'])
    
    def test_link_missing_provider_user_id_fails(self):
        """Test that linking without provider_user_id fails."""
        link_data = {
            'provider': 'google'
        }
        
        response = self.client.post(self.link_url, link_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('provider_user_id', response.data['details'])
