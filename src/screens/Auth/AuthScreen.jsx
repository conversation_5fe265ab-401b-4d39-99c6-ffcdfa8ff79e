import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import LoginForm from '../../components/auth/LoginForm';
import RegisterForm from '../../components/auth/RegisterForm';
import ForgotPasswordForm from '../../components/auth/ForgotPasswordForm';
import './AuthScreen.css';

export default function AuthScreen() {
  const { login, register, requestPasswordReset, isAuthenticated, isLoading, error } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Auth modes: 'login', 'register', 'forgot-password'
  const [authMode, setAuthMode] = useState(() => {
    const mode = searchParams.get('mode');
    return ['login', 'register', 'forgot-password'].includes(mode) ? mode : 'login';
  });

  const [resetSuccess, setResetSuccess] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/groups', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleLogin = async (credentials) => {
    try {
      await login(credentials);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleRegister = async (userData) => {
    try {
      await register(userData);
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  const handleForgotPassword = async (email) => {
    try {
      await requestPasswordReset(email);
      setResetSuccess(true);
    } catch (error) {
      console.error('Password reset request failed:', error);
    }
  };

  const renderAuthContent = () => {
    if (authMode === 'login') {
      return (
        <div className="auth-screen__email">
          <LoginForm
            onSubmit={handleLogin}
            loading={isLoading}
            error={error}
          />

          <div className="auth-screen__links">
            <button
              className="auth-link"
              onClick={() => setAuthMode('forgot-password')}
            >
              Forgot your password?
            </button>

            <div className="auth-divider">or</div>

            <button
              className="auth-link auth-link--center"
              onClick={() => setAuthMode('register')}
            >
              Don't have an account? Sign up
            </button>

            <button
              className="auth-link auth-link--center"
              onClick={() => navigate('/telegram')}
            >
              Continue with Telegram instead
            </button>
          </div>
        </div>
      );
    }

    if (authMode === 'register') {
      return (
        <div className="auth-screen__email">
          <RegisterForm
            onSubmit={handleRegister}
            loading={isLoading}
            error={error}
          />

          <div className="auth-screen__links">
            <button
              className="auth-link auth-link--center"
              onClick={() => setAuthMode('login')}
            >
              Already have an account? Sign in
            </button>

            <button
              className="auth-link auth-link--center"
              onClick={() => navigate('/telegram')}
            >
              Continue with Telegram instead
            </button>
          </div>
        </div>
      );
    }

    if (authMode === 'forgot-password') {
      return (
        <div className="auth-screen__email">
          <ForgotPasswordForm
            onSubmit={handleForgotPassword}
            loading={isLoading}
            error={error}
            success={resetSuccess}
          />

          <div className="auth-screen__links">
            <button
              className="auth-link auth-link--center"
              onClick={() => {
                setAuthMode('login');
                setResetSuccess(false);
              }}
            >
              Back to sign in
            </button>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="auth-screen">
      <div className="auth-screen__content">
        <div className="auth-screen__logo">
          <div className="auth-screen__icon">💰</div>
          <h1 className="auth-screen__title">Spliton</h1>
          <p className="auth-screen__subtitle">
            Split expenses with friends easily
          </p>
        </div>

        <Card className="auth-screen__card">
          {renderAuthContent()}

          <div className="auth-screen__features">
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">👥</span>
              <span>Create groups with friends</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">💳</span>
              <span>Track shared expenses</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">⚖️</span>
              <span>Automatic balance calculation</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">💸</span>
              <span>Easy settlement tracking</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
