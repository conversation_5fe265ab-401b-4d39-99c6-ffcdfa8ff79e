/* Profile Screen Styles */

.profile-screen {
  min-height: 100vh;
  background-color: var(--tg-theme-secondary-bg-color, #f8f9fa);
  padding: 16px;
}

.profile-screen__header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 0 8px;
}

.profile-screen__header h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000);
  margin: 0;
}

.profile-screen__back {
  padding: 8px 12px;
  font-size: 16px;
}

.profile-screen__content {
  max-width: 600px;
  margin: 0 auto;
}

.profile-screen__card {
  padding: 24px;
}

.profile-screen__loading {
  text-align: center;
  padding: 48px 16px;
  color: var(--tg-theme-hint-color, #999);
}

.profile-screen__error {
  background-color: var(--tg-theme-destructive-text-color, #ff3b30);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

.profile-screen__avatar {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--tg-theme-secondary-bg-color, #e5e5e5);
}

.profile-screen__avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--tg-theme-button-color, #007aff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 600;
  margin: 0 auto 16px;
}

.profile-screen__avatar h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000);
  margin: 0 0 8px 0;
}

.profile-screen__email {
  font-size: 16px;
  color: var(--tg-theme-hint-color, #999);
  margin: 0;
}

.profile-screen__form {
  margin-bottom: 32px;
}

.profile-screen__fields {
  margin-bottom: 24px;
}

.profile-screen__fields .tg-input-wrapper {
  margin-bottom: 16px;
}

.profile-screen__fields .tg-input-wrapper:last-child {
  margin-bottom: 0;
}

.profile-screen__email-field {
  position: relative;
}

.profile-screen__email-status {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.profile-screen__email-status--verified {
  background-color: var(--tg-theme-button-color, #007aff);
  color: white;
}

.profile-screen__verify-email {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  padding: 6px 12px;
  height: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-screen__info {
  margin-bottom: 32px;
}

.profile-screen__section {
  margin-bottom: 32px;
}

.profile-screen__section:last-child {
  margin-bottom: 0;
}

.profile-screen__section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000);
  margin: 0 0 16px 0;
}

.profile-screen__actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 24px;
}

.profile-screen__actions button {
  flex: 1;
  min-width: 120px;
}

.profile-screen__save {
  background-color: var(--tg-theme-button-color, #007aff);
}

.profile-screen__edit {
  background-color: var(--tg-theme-button-color, #007aff);
}

.profile-screen__danger-zone {
  padding-top: 24px;
  border-top: 1px solid var(--tg-theme-secondary-bg-color, #e5e5e5);
}

.profile-screen__logout {
  width: 100%;
  background-color: var(--tg-theme-destructive-text-color, #ff3b30);
  color: white;
}

.profile-screen__logout:hover {
  background-color: #e6342a;
}

/* Cell component enhancements for profile */
.tg-cell {
  margin-bottom: 8px;
}

.tg-cell:last-child {
  margin-bottom: 0;
}

.tg-cell__badge {
  background-color: var(--tg-theme-button-color, #007aff);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .profile-screen {
    padding: 12px;
  }
  
  .profile-screen__header {
    margin-bottom: 16px;
  }
  
  .profile-screen__card {
    padding: 16px;
  }
  
  .profile-screen__avatar-circle {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }
  
  .profile-screen__actions {
    flex-direction: column;
  }
  
  .profile-screen__actions button {
    flex: none;
    width: 100%;
  }
}
