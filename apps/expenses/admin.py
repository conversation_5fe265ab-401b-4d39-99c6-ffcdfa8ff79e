from django.contrib import admin
from .models import Group, Membership, Expense, ExpenseParticipant, Settlement


class MembershipInline(admin.TabularInline):
    model = Membership
    extra = 0


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_by', 'telegram_chat_id', 'created_at', 'is_published', 'is_deleted']
    list_filter = ['created_at', 'is_published', 'is_deleted']
    search_fields = ['name', 'created_by__username']
    readonly_fields = ['id', 'created_at', 'updated_at']
    inlines = [MembershipInline]

    def save_model(self, request, obj, form, change):
        """
        Save the group and ensure the creator is added as an admin member.
        """
        is_new = obj.pk is None

        # If created_by is not set, set it to the current user
        if not obj.created_by:
            obj.created_by = request.user

        super().save_model(request, obj, form, change)

        # If this is a new group, add the creator as an admin member
        if is_new:
            Membership.objects.get_or_create(
                user=obj.created_by,
                group=obj,
                defaults={'role': 'admin'}
            )


@admin.register(Membership)
class MembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'role', 'joined_at', 'is_published']
    list_filter = ['role', 'joined_at', 'is_published']
    search_fields = ['user__username', 'group__name']
    readonly_fields = ['id']


class ExpenseParticipantInline(admin.TabularInline):
    model = ExpenseParticipant
    extra = 0


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    list_display = ['description', 'group', 'amount_major_units', 'currency', 'created_by', 'occurred_at']
    list_filter = ['currency', 'occurred_at', 'created_at', 'is_published']
    search_fields = ['description', 'group__name', 'created_by__username']
    readonly_fields = ['id', 'created_at', 'updated_at']
    inlines = [ExpenseParticipantInline]


@admin.register(ExpenseParticipant)
class ExpenseParticipantAdmin(admin.ModelAdmin):
    list_display = ['user', 'expense', 'share_major_units', 'paid_major_units', 'net_major_units']
    list_filter = ['expense__currency', 'expense__occurred_at', 'is_published']
    search_fields = ['user__username', 'expense__description']
    readonly_fields = ['id']


@admin.register(Settlement)
class SettlementAdmin(admin.ModelAdmin):
    list_display = ['payer', 'payee', 'group', 'amount_major_units', 'currency', 'settled_at']
    list_filter = ['currency', 'settled_at', 'created_at', 'is_published']
    search_fields = ['payer__username', 'payee__username', 'group__name']
    readonly_fields = ['id', 'created_at']
