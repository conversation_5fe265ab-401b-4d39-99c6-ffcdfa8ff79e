# Generated by Django 5.2.6 on 2025-09-22 02:43

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('telegram_chat_id', models.BigIntegerField(blank=True, db_index=True, null=True)),
                ('is_published', models.BooleanField(default=True, help_text='Whether this group should be visible in API responses')),
                ('is_deleted', models.BooleanField(default=False, help_text='Soft delete flag - group is marked as deleted but not removed to preserve expense history')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_groups', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'groups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('description', models.CharField(max_length=500)),
                ('amount', models.PositiveIntegerField(help_text='Amount in minor currency units (e.g., cents) to avoid floating point issues', validators=[django.core.validators.MinValueValidator(1)])),
                ('currency', models.CharField(choices=[('USD', 'US Dollar'), ('EUR', 'Euro'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('AUD', 'Australian Dollar'), ('IRR', 'Iranian Rial')], default='USD', max_length=3)),
                ('is_published', models.BooleanField(default=True, help_text='Whether this expense should be visible in API responses')),
                ('occurred_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_expenses', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='expenses.group')),
            ],
            options={
                'db_table': 'expenses',
                'ordering': ['-occurred_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Settlement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.PositiveIntegerField(help_text='Settlement amount in minor currency units (e.g., cents)', validators=[django.core.validators.MinValueValidator(1)])),
                ('currency', models.CharField(choices=[('USD', 'US Dollar'), ('EUR', 'Euro'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('AUD', 'Australian Dollar'), ('IRR', 'Iranian Rial')], default='USD', max_length=3)),
                ('note', models.TextField(blank=True, null=True)),
                ('is_published', models.BooleanField(default=True, help_text='Whether this settlement should be visible in API responses')),
                ('settled_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='settlements', to='expenses.group')),
                ('payee', models.ForeignKey(help_text='User who received the payment', on_delete=django.db.models.deletion.CASCADE, related_name='payments_received', to=settings.AUTH_USER_MODEL)),
                ('payer', models.ForeignKey(help_text='User who made the payment', on_delete=django.db.models.deletion.CASCADE, related_name='payments_made', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'settlements',
                'ordering': ['-settled_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseParticipant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('share', models.PositiveIntegerField(help_text='Amount this user owes for this expense in minor currency units (e.g., cents)', validators=[django.core.validators.MinValueValidator(0)])),
                ('paid', models.PositiveIntegerField(default=0, help_text='Amount this user actually paid for this expense in minor currency units (e.g., cents)', validators=[django.core.validators.MinValueValidator(0)])),
                ('is_published', models.BooleanField(default=True, help_text='Whether this participation should be visible in API responses')),
                ('expense', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='expenses.expense')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expense_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'expense_participants',
                'ordering': ['expense', 'user'],
                'unique_together': {('expense', 'user')},
            },
        ),
        migrations.CreateModel(
            name='Membership',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('member', 'Member'), ('admin', 'Admin')], default='member', max_length=10)),
                ('is_published', models.BooleanField(default=True, help_text='Whether this membership should be visible in API responses')),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='expenses.group')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'memberships',
                'ordering': ['-joined_at'],
                'unique_together': {('user', 'group')},
            },
        ),
    ]
