from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, AuthenticationProvider


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom User admin."""
    list_display = ['username', 'email', 'first_name', 'last_name', 'profile_photo', 'is_active', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    readonly_fields = ['date_joined', 'last_login', 'created_at', 'updated_at']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Profile Information', {
            'fields': ('profile_photo',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
        ('Publication Control', {
            'fields': ('is_published', 'is_deleted')
        }),
    )


@admin.register(AuthenticationProvider)
class AuthenticationProviderAdmin(admin.ModelAdmin):
    list_display = ['user', 'provider', 'provider_username', 'provider_email', 'is_primary', 'is_verified', 'created_at']
    list_filter = ['provider', 'is_primary', 'is_verified', 'created_at']
    search_fields = ['user__username', 'user__email', 'provider_username', 'provider_email', 'provider_user_id']
    readonly_fields = ['created_at', 'updated_at', 'linked_at', 'last_used_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'provider', 'provider_user_id')
        }),
        ('Provider Details', {
            'fields': ('provider_username', 'provider_email', 'provider_data')
        }),
        ('Status', {
            'fields': ('is_primary', 'is_verified', 'is_published', 'is_deleted')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'linked_at', 'last_used_at')
        }),
    )
