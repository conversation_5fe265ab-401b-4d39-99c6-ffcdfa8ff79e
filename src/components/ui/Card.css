.tg-card {
  background-color: var(--tg-section-bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  overflow: hidden;
}

.tg-card--clickable {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.tg-card--clickable:hover {
  box-shadow: var(--shadow-md);
}

.tg-card--clickable:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

/* Padding variants */
.tg-card--padding-none {
  padding: 0;
}

.tg-card--padding-small .tg-card__content {
  padding: var(--spacing-md);
}

.tg-card--padding-medium .tg-card__content {
  padding: var(--spacing-lg);
}

.tg-card--padding-large .tg-card__content {
  padding: var(--spacing-xl);
}

.tg-card__header {
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.tg-card--padding-small .tg-card__header {
  padding: var(--spacing-md) var(--spacing-md) 0;
}

.tg-card--padding-large .tg-card__header {
  padding: var(--spacing-xl) var(--spacing-xl) 0;
}

.tg-card__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--tg-text-color);
  margin-bottom: var(--spacing-xs);
}

.tg-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--tg-subtitle-text-color);
}

.tg-card__content {
  /* Padding is set by variants above */
}

/* Balance card variant */
.tg-card--balance {
  background: linear-gradient(135deg, var(--tg-button-color), #1a6bb8);
  color: white;
}

.tg-card--balance .tg-card__title,
.tg-card--balance .tg-card__subtitle {
  color: white;
}

.tg-card--balance .tg-card__subtitle {
  opacity: 0.8;
}

/* Expense card variant */
.tg-card--expense {
  border-left: 4px solid var(--tg-button-color);
}

/* Warning card variant */
.tg-card--warning {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
}

.dark-theme .tg-card--warning {
  background-color: #332701;
  border-left-color: #ffc107;
}

/* Error card variant */
.tg-card--error {
  background-color: #f8d7da;
  border-left: 4px solid var(--tg-destructive-text-color);
}

.dark-theme .tg-card--error {
  background-color: #2c0b0e;
  border-left-color: var(--tg-destructive-text-color);
}

/* Success card variant */
.tg-card--success {
  background-color: #d1edff;
  border-left: 4px solid #28a745;
}

.dark-theme .tg-card--success {
  background-color: #0a2e0a;
  border-left-color: #28a745;
}
