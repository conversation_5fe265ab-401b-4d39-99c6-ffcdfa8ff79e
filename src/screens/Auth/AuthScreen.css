.auth-screen {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--tg-button-color, #007aff), #1a6bb8);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg, 24px);
}

/* Browser mode specific styles */
body.browser-mode .auth-screen {
  background: linear-gradient(135deg, #007aff, #1a6bb8);
}

.auth-screen__content {
  width: 100%;
  max-width: 400px;
}

.auth-screen__logo {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: white;
}

.auth-screen__icon {
  font-size: 64px;
  margin-bottom: var(--spacing-md);
}

.auth-screen__title {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin: 0 0 var(--spacing-sm) 0;
}

.auth-screen__subtitle {
  font-size: var(--font-size-md);
  opacity: 0.9;
  margin: 0;
}

.auth-screen__card {
  background-color: var(--tg-section-bg-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.auth-screen__welcome h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--tg-text-color);
  margin: 0 0 var(--spacing-md) 0;
}

.auth-screen__welcome p {
  color: var(--tg-hint-color);
  line-height: 1.5;
  margin: 0 0 var(--spacing-lg) 0;
}

.auth-screen__error {
  background-color: rgba(255, 59, 48, 0.1);
  border: 1px solid var(--tg-destructive-text-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.auth-screen__error p {
  color: var(--tg-destructive-text-color);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-sm);
}

.auth-screen__error p:last-child {
  margin-bottom: 0;
}

.auth-screen__actions {
  margin-bottom: var(--spacing-xl);
}

.auth-screen__features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.auth-screen__feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--tg-hint-color);
  font-size: var(--font-size-sm);
}

.auth-screen__feature-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}
