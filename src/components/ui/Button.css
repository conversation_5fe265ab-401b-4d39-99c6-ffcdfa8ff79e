.tg-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Variants */
.tg-button--primary {
  background-color: var(--tg-button-color);
  color: var(--tg-button-text-color);
}

.tg-button--primary:hover:not(.tg-button--disabled) {
  opacity: 0.9;
}

.tg-button--primary:active:not(.tg-button--disabled) {
  opacity: 0.8;
}

.tg-button--secondary {
  background-color: var(--tg-secondary-bg-color);
  color: var(--tg-text-color);
}

.tg-button--secondary:hover:not(.tg-button--disabled) {
  opacity: 0.9;
}

.tg-button--secondary:active:not(.tg-button--disabled) {
  opacity: 0.8;
}

.tg-button--ghost {
  background-color: transparent;
  color: var(--tg-link-color);
}

.tg-button--ghost:hover:not(.tg-button--disabled) {
  background-color: var(--tg-secondary-bg-color);
}

.tg-button--ghost:active:not(.tg-button--disabled) {
  opacity: 0.7;
}

.tg-button--destructive {
  background-color: var(--tg-destructive-text-color);
  color: white;
}

.tg-button--destructive:hover:not(.tg-button--disabled) {
  opacity: 0.9;
}

.tg-button--destructive:active:not(.tg-button--disabled) {
  opacity: 0.8;
}

/* Sizes */
.tg-button--small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

.tg-button--medium {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-md);
  min-height: 44px;
}

.tg-button--large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* States */
.tg-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tg-button--loading {
  cursor: not-allowed;
}

.tg-button--loading .tg-button__content {
  opacity: 0.7;
}

/* Spinner */
.tg-button__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: tg-button-spin 1s linear infinite;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@keyframes tg-button-spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.tg-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

/* Full width variant */
.tg-button--full-width {
  width: 100%;
}
