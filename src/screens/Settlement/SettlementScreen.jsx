import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { groupsAPI, settlementsAPI } from '../../services/api';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Cell from '../../components/ui/Cell';
import Card from '../../components/ui/Card';
import { formatCurrency, parseCurrencyInput, toMinorUnits } from '../../utils/currency';
import { initBackButton } from '../../utils/telegram';
import './SettlementScreen.css';

export default function SettlementScreen() {
  const { groupId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [group, setGroup] = useState(null);
  const [members, setMembers] = useState([]);
  const [balances, setBalances] = useState([]);
  const [suggestedTransfers, setSuggestedTransfers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    defaultValues: {
      amount: '',
      from_user: '',
      to_user: ''
    }
  });

  useEffect(() => {
    // Initialize Telegram back button
    const cleanup = initBackButton(() => {
      navigate(`/groups/${groupId}`);
    });

    loadSettlementData();

    return cleanup;
  }, [groupId, navigate]);

  const loadSettlementData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [groupData, membersData, balancesData, transfersData] = await Promise.all([
        groupsAPI.getGroup(groupId),
        groupsAPI.getGroupMembers(groupId),
        groupsAPI.getGroupBalances(groupId),
        groupsAPI.getSuggestedTransfers(groupId)
      ]);

      setGroup(groupData);
      setMembers(membersData);
      setBalances(balancesData);
      setSuggestedTransfers(transfersData);
    } catch (error) {
      console.error('Failed to load settlement data:', error);
      setError('Failed to load settlement data');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickSettle = async (transfer) => {
    try {
      setSubmitting(true);

      const settlementData = {
        amount: transfer.amount,
        from_user: transfer.from_user_id,
        to_user: transfer.to_user_id
      };

      await settlementsAPI.createSettlement(groupId, settlementData);

      // Reload data to get updated balances
      await loadSettlementData();
    } catch (error) {
      console.error('Failed to record settlement:', error);
      setError('Failed to record settlement');
    } finally {
      setSubmitting(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      setSubmitting(true);

      const settlementData = {
        amount: toMinorUnits(parseCurrencyInput(data.amount)),
        from_user: data.from_user,
        to_user: data.to_user
      };

      await settlementsAPI.createSettlement(groupId, settlementData);

      reset();
      await loadSettlementData();
    } catch (error) {
      console.error('Failed to record settlement:', error);
      setError(error.message || 'Failed to record settlement');
    } finally {
      setSubmitting(false);
    }
  };

  const getUserBalance = (userId) => {
    const userBalance = balances.find(balance => balance.user_id === userId);
    return userBalance ? userBalance.balance : 0;
  };

  const getMemberName = (userId) => {
    const member = members.find(m => m.id === userId);
    return member ? `${member.first_name} ${member.last_name || ''}`.trim() : 'Unknown';
  };

  if (loading) {
    return (
      <div className="screen">
        <div className="loading">Loading settlement data...</div>
      </div>
    );
  }

  if (error && !group) {
    return (
      <div className="screen">
        <div className="error">
          <p>{error}</p>
          <Button onClick={loadSettlementData}>Try Again</Button>
        </div>
      </div>
    );
  }

  const userBalance = getUserBalance(user?.id);
  const hasDebts = balances.some(balance => balance.balance !== 0);

  return (
    <div className="screen">
      <div className="screen__header">
        <h1 className="screen__title">Settle Up</h1>
      </div>

      <div className="screen__content">
        <div className="container">
          {/* Current Balance */}
          <Card className="settlement__balance-card">
            <div className="settlement__balance">
              <div className="settlement__balance-label">Your balance</div>
              <div className={`settlement__balance-amount ${
                userBalance > 0 ? 'settlement__balance-amount--positive' :
                userBalance < 0 ? 'settlement__balance-amount--negative' :
                'settlement__balance-amount--neutral'
              }`}>
                {userBalance === 0 ? 'Settled up' : formatCurrency(Math.abs(userBalance))}
              </div>
              {userBalance !== 0 && (
                <div className="settlement__balance-status text-hint">
                  {userBalance > 0 ? 'You are owed' : 'You owe'}
                </div>
              )}
            </div>
          </Card>

          {/* Suggested Transfers */}
          {suggestedTransfers.length > 0 && (
            <div className="section">
              <div className="section__header">Suggested Settlements</div>
              <div className="section__content">
                {suggestedTransfers.map((transfer, index) => (
                  <Cell
                    key={index}
                    title={`${getMemberName(transfer.from_user_id)} pays ${getMemberName(transfer.to_user_id)}`}
                    subtitle="Tap to record this settlement"
                    after={
                      <div className="settlement__transfer-actions">
                        <div className="settlement__transfer-amount">
                          {formatCurrency(transfer.amount)}
                        </div>
                        <Button
                          size="small"
                          variant="primary"
                          onClick={() => handleQuickSettle(transfer)}
                          disabled={submitting}
                        >
                          Settle
                        </Button>
                      </div>
                    }
                  />
                ))}
              </div>
            </div>
          )}

          {/* Manual Settlement Form */}
          <Card className="settlement__manual-form">
            <div className="settlement__form-header">
              <h3>Record Manual Settlement</h3>
            </div>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="form-field">
                <Input
                  label="Amount"
                  placeholder="0.00"
                  type="number"
                  step="0.01"
                  min="0"
                  error={errors.amount?.message}
                  {...register('amount', {
                    required: 'Amount is required',
                    min: {
                      value: 0.01,
                      message: 'Amount must be greater than 0'
                    }
                  })}
                />
              </div>

              <div className="form-field">
                <label className="tg-input__label">From (who paid)</label>
                <div className="settlement__user-selector">
                  {members.map((member) => (
                    <Cell
                      key={`from-${member.id}`}
                      title={`${member.first_name} ${member.last_name || ''}`.trim()}
                      subtitle={member.username ? `@${member.username}` : ''}
                      before={
                        <input
                          type="radio"
                          value={member.id}
                          {...register('from_user', { required: 'Please select who paid' })}
                        />
                      }
                    />
                  ))}
                </div>
              </div>

              <div className="form-field">
                <label className="tg-input__label">To (who received)</label>
                <div className="settlement__user-selector">
                  {members.map((member) => (
                    <Cell
                      key={`to-${member.id}`}
                      title={`${member.first_name} ${member.last_name || ''}`.trim()}
                      subtitle={member.username ? `@${member.username}` : ''}
                      before={
                        <input
                          type="radio"
                          value={member.id}
                          {...register('to_user', { required: 'Please select who received' })}
                        />
                      }
                    />
                  ))}
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="error-message">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={submitting}
                disabled={submitting}
                className="tg-button--full-width"
              >
                Record Settlement
              </Button>
            </form>
          </Card>

          {/* All Balances */}
          {hasDebts && (
            <div className="section">
              <div className="section__header">All Balances</div>
              <div className="section__content">
                {balances.map((balance) => {
                  const member = members.find(m => m.id === balance.user_id);
                  if (!member || balance.balance === 0) return null;

                  return (
                    <Cell
                      key={balance.user_id}
                      title={`${member.first_name} ${member.last_name || ''}`.trim()}
                      subtitle={member.username ? `@${member.username}` : ''}
                      after={
                        <div className={`settlement__balance-display ${
                          balance.balance > 0 ? 'settlement__balance-display--positive' :
                          'settlement__balance-display--negative'
                        }`}>
                          {balance.balance > 0 ? '+' : ''}{formatCurrency(balance.balance)}
                        </div>
                      }
                    />
                  );
                })}
              </div>
            </div>
          )}

          {!hasDebts && (
            <div className="empty-state">
              <div className="empty-state__icon">✅</div>
              <div className="empty-state__title">All Settled Up!</div>
              <div className="empty-state__description">
                Everyone in this group is settled up. No payments needed.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
