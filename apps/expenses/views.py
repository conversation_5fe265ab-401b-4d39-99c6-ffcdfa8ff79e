from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.shortcuts import get_object_or_404

from .models import Group, Membership, Expense, Settlement
from .serializers import (
    GroupSerializer, AddMemberSerializer, ExpenseSerializer, SettlementSerializer
)
from .permissions import IsGroupMember, IsGroupAdmin
from .services import group_net_balances, simplify_transfers


class GroupViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing groups.
    """
    serializer_class = GroupSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return groups where user is a member."""
        return Group.objects.filter(
            memberships__user=self.request.user,
            memberships__is_published=True,
            is_published=True,
            is_deleted=False
        ).distinct().prefetch_related('memberships__user')

    def get_permissions(self):
        """
        Instantiate and return the list of permissions that this view requires.
        """
        if self.action in ['update', 'partial_update', 'destroy']:
            permission_classes = [IsAuthenticated, IsGroupAdmin]
        elif self.action in ['retrieve', 'balances']:
            permission_classes = [IsAuthenticated, IsGroupMember]
        else:
            permission_classes = [IsAuthenticated]

        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['post'])
    def add_member(self, request, pk=None):
        """Add a member to the group."""
        group = self.get_object()

        # Check if user is admin
        if not IsGroupAdmin().has_object_permission(request, self, group):
            return Response(
                {'error': 'Only group admins can add members.'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = AddMemberSerializer(data=request.data, context={'group': group})
        if serializer.is_valid():
            user_id = serializer.validated_data['user_id']
            role = serializer.validated_data['role']

            membership = Membership.objects.create(
                user_id=user_id,
                group=group,
                role=role
            )

            return Response(
                {'message': 'Member added successfully.'},
                status=status.HTTP_201_CREATED
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def balances(self, request, pk=None):
        """Get group balances and suggested transfers."""
        group = self.get_object()

        # Calculate balances
        balances = group_net_balances(group)

        # Get suggested transfers
        suggested_transfers = simplify_transfers(balances)

        return Response({
            'balances': balances,
            'suggested_transfers': suggested_transfers
        })


class ExpenseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing expenses.
    """
    serializer_class = ExpenseSerializer
    permission_classes = [IsAuthenticated, IsGroupMember]

    def get_queryset(self):
        """Return expenses for groups where user is a member."""
        return Expense.objects.filter(
            group__memberships__user=self.request.user
        ).distinct().select_related('group', 'created_by').prefetch_related('participants__user')

    def perform_create(self, serializer):
        """Ensure user can only create expenses in groups they belong to."""
        group = serializer.validated_data['group']

        # Check if user is a member of the group
        if not Membership.objects.filter(user=self.request.user, group=group).exists():
            raise PermissionError("You can only create expenses in groups you belong to.")

        serializer.save()


class SettlementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing settlements.
    """
    serializer_class = SettlementSerializer
    permission_classes = [IsAuthenticated, IsGroupMember]

    def get_queryset(self):
        """Return settlements for groups where user is a member."""
        return Settlement.objects.filter(
            group__memberships__user=self.request.user
        ).distinct().select_related('group', 'payer', 'payee')

    def perform_create(self, serializer):
        """Ensure user can only create settlements in groups they belong to."""
        group = serializer.validated_data['group']

        # Check if user is a member of the group
        if not Membership.objects.filter(user=self.request.user, group=group).exists():
            raise PermissionError("You can only create settlements in groups you belong to.")

        serializer.save()
