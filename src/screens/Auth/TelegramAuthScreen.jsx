import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { isTelegramWebApp } from '../../utils/telegram';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import './AuthScreen.css';

export default function TelegramAuthScreen() {
  const { login, isAuthenticated, isLoading, error } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/groups', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleTelegramLogin = async () => {
    try {
      await login(); // This will use the Telegram login
    } catch (error) {
      console.error('Telegram login failed:', error);
    }
  };

  // If not in Telegram WebApp, show message and redirect option
  if (!isTelegramWebApp()) {
    return (
      <div className="auth-screen">
        <div className="auth-screen__content">
          <div className="auth-screen__logo">
            <div className="auth-screen__icon">💰</div>
            <h1 className="auth-screen__title">Spliton</h1>
            <p className="auth-screen__subtitle">
              Split expenses with friends easily
            </p>
          </div>

          <Card className="auth-screen__card">
            <div className="auth-screen__welcome">
              <h2>Telegram Required</h2>
              <p>
                This page is designed for Telegram WebApp authentication. 
                Please open this app from within Telegram to use this feature.
              </p>
            </div>

            <div className="auth-screen__error">
              <p>This feature is only available in Telegram WebApp</p>
              <p className="text-hint">
                If you want to use email authentication, please use the main login page.
              </p>
            </div>

            <div className="auth-screen__actions">
              <Button
                variant="primary"
                size="large"
                onClick={() => navigate('/auth')}
                className="tg-button--full-width"
              >
                Go to Email Login
              </Button>
            </div>

            <div className="auth-screen__features">
              <div className="auth-screen__feature">
                <span className="auth-screen__feature-icon">👥</span>
                <span>Create groups with friends</span>
              </div>
              <div className="auth-screen__feature">
                <span className="auth-screen__feature-icon">💳</span>
                <span>Track shared expenses</span>
              </div>
              <div className="auth-screen__feature">
                <span className="auth-screen__feature-icon">⚖️</span>
                <span>Automatic balance calculation</span>
              </div>
              <div className="auth-screen__feature">
                <span className="auth-screen__feature-icon">💸</span>
                <span>Easy settlement tracking</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-screen">
      <div className="auth-screen__content">
        <div className="auth-screen__logo">
          <div className="auth-screen__icon">💰</div>
          <h1 className="auth-screen__title">Spliton</h1>
          <p className="auth-screen__subtitle">
            Split expenses with friends easily
          </p>
        </div>

        <Card className="auth-screen__card">
          <div className="auth-screen__welcome">
            <h2>Welcome to Spliton</h2>
            <p>
              Track shared expenses, split bills, and settle up with friends.
              Connect with your Telegram account to get started.
            </p>
          </div>

          {error && (
            <div className="auth-screen__error">
              <p>Authentication failed: {error}</p>
              <p className="text-hint">
                Make sure you're opening this app from Telegram.
              </p>
            </div>
          )}

          <div className="auth-screen__actions">
            <Button
              variant="primary"
              size="large"
              loading={isLoading}
              onClick={handleTelegramLogin}
              className="tg-button--full-width"
            >
              Continue with Telegram
            </Button>
          </div>

          <div className="auth-divider">or</div>

          <button
            className="auth-link auth-link--center"
            onClick={() => navigate('/auth')}
          >
            Use email and password instead
          </button>

          <div className="auth-screen__features">
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">👥</span>
              <span>Create groups with friends</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">💳</span>
              <span>Track shared expenses</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">⚖️</span>
              <span>Automatic balance calculation</span>
            </div>
            <div className="auth-screen__feature">
              <span className="auth-screen__feature-icon">💸</span>
              <span>Easy settlement tracking</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
