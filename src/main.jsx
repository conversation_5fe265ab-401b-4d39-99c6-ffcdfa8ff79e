import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Initialize Telegram WebApp theme or set default theme
function initializeTheme() {
  if (window.Telegram?.WebApp) {
    const tg = window.Telegram.WebApp;
    tg.ready();
    tg.expand();

    // Apply theme
    const isDark = tg.colorScheme === 'dark';
    document.body.classList.toggle('dark-theme', isDark);

    // Set CSS variables from Telegram theme
    if (tg.themeParams) {
      const root = document.documentElement;
      Object.entries(tg.themeParams).forEach(([key, value]) => {
        if (value) {
          root.style.setProperty(`--tg-${key.replace(/_/g, '-')}`, value);
        }
      });
    }
  } else {
    // Set default theme for regular browser
    const root = document.documentElement;
    const defaultTheme = {
      'bg-color': '#ffffff',
      'text-color': '#000000',
      'hint-color': '#999999',
      'link-color': '#007aff',
      'button-color': '#007aff',
      'button-text-color': '#ffffff',
      'secondary-bg-color': '#f8f9fa',
      'section-bg-color': '#ffffff',
      'destructive-text-color': '#ff3b30'
    };

    Object.entries(defaultTheme).forEach(([key, value]) => {
      root.style.setProperty(`--tg-${key}`, value);
    });

    // Add a class to indicate we're in browser mode
    document.body.classList.add('browser-mode');
  }
}

initializeTheme();

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
