import json
import hmac
import hashlib
from urllib.parse import urlencode
from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from apps.accounts.models import User, AuthenticationProvider
from .utils import verify_telegram_webapp_data, create_or_update_user_from_telegram


class TelegramWebAppUtilsTestCase(TestCase):
    """Test Telegram WebApp utility functions."""

    @override_settings(TELEGRAM_BOT_TOKEN='123456:ABC-DEF')
    def test_verify_telegram_webapp_data_valid(self):
        """Test verification of valid Telegram WebApp data."""
        # Create test data
        user_data = {
            'id': 12345,
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }

        auth_date = '**********'

        # Create init_data string
        data_dict = {
            'user': json.dumps(user_data),
            'auth_date': auth_date
        }

        # Create data check string
        data_check_arr = []
        for key in sorted(data_dict.keys()):
            data_check_arr.append(f"{key}={data_dict[key]}")
        data_check_string = '\n'.join(data_check_arr)

        # Create secret key and hash
        secret_key = hmac.new(
            "WebAppData".encode(),
            '123456:ABC-DEF'.encode(),
            hashlib.sha256
        ).digest()

        hash_value = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()

        # Create init_data with hash
        data_dict['hash'] = hash_value
        init_data = urlencode(data_dict)

        # Test verification
        result = verify_telegram_webapp_data(init_data)

        self.assertIsNotNone(result)
        self.assertEqual(result['id'], 12345)
        self.assertEqual(result['username'], 'testuser')

    @override_settings(TELEGRAM_BOT_TOKEN='123456:ABC-DEF')
    def test_verify_telegram_webapp_data_invalid_hash(self):
        """Test verification fails with invalid hash."""
        init_data = 'user=%7B%22id%22%3A12345%7D&auth_date=**********&hash=invalid_hash'

        result = verify_telegram_webapp_data(init_data)

        self.assertIsNone(result)

    def test_create_or_update_user_from_telegram_new_user(self):
        """Test creating new user from Telegram data."""
        telegram_data = {
            'id': 12345,
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'language_code': 'en'
        }

        user, created = create_or_update_user_from_telegram(telegram_data)

        self.assertTrue(created)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')

        # Check AuthenticationProvider
        auth_provider = user.auth_providers.filter(provider='telegram').first()
        self.assertIsNotNone(auth_provider)
        self.assertEqual(auth_provider.provider_user_id, '12345')
        self.assertEqual(auth_provider.provider_username, 'testuser')

    def test_create_or_update_user_from_telegram_existing_user(self):
        """Test updating existing user from Telegram data."""
        # Create existing user and auth provider
        user = User.objects.create_user(username='testuser', first_name='Old')
        AuthenticationProvider.objects.create(
            user=user,
            provider='telegram',
            provider_user_id='12345',
            provider_username='oldusername'
        )

        telegram_data = {
            'id': 12345,
            'username': 'newusername',
            'first_name': 'New',
            'last_name': 'Name',
            'language_code': 'en'
        }

        updated_user, created = create_or_update_user_from_telegram(telegram_data)

        self.assertFalse(created)
        self.assertEqual(updated_user.id, user.id)

        # Check updated auth provider
        auth_provider = updated_user.auth_providers.filter(provider='telegram').first()
        self.assertEqual(auth_provider.provider_username, 'newusername')
        self.assertEqual(auth_provider.provider_data['first_name'], 'New')


class TelegramWebAppVerifyAPITestCase(APITestCase):
    """Test Telegram WebApp verification API endpoint."""

    def setUp(self):
        self.url = reverse('authx:telegram_webapp_verify')

    @override_settings(TELEGRAM_BOT_TOKEN='123456:ABC-DEF')
    def test_telegram_webapp_verify_success(self):
        """Test successful Telegram WebApp verification."""
        # Create valid init_data (simplified for testing)
        user_data = {'id': 12345, 'username': 'testuser', 'first_name': 'Test'}
        auth_date = '**********'

        data_dict = {
            'user': json.dumps(user_data),
            'auth_date': auth_date
        }

        # Create proper hash
        data_check_string = '\n'.join(f"{k}={v}" for k, v in sorted(data_dict.items()))
        secret_key = hmac.new("WebAppData".encode(), '123456:ABC-DEF'.encode(), hashlib.sha256).digest()
        hash_value = hmac.new(secret_key, data_check_string.encode(), hashlib.sha256).hexdigest()

        data_dict['hash'] = hash_value
        init_data = urlencode(data_dict)

        response = self.client.post(self.url, {'initData': init_data})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertTrue(response.data['created'])

    def test_telegram_webapp_verify_invalid_data(self):
        """Test verification fails with invalid data."""
        response = self.client.post(self.url, {'initData': 'invalid_data'})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_telegram_webapp_verify_missing_data(self):
        """Test verification fails with missing data."""
        response = self.client.post(self.url, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
