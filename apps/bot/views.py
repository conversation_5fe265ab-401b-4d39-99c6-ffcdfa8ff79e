import json
import logging
import requests
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.conf import settings

logger = logging.getLogger(__name__)


@csrf_exempt
@require_POST
def webhook(request):
    """
    Telegram bot webhook endpoint.

    Handles incoming updates from Telegram and responds to /start command
    with a WebApp button.
    """
    try:
        # Parse the incoming update
        update = json.loads(request.body.decode('utf-8'))

        # Log the update for debugging (remove in production)
        if settings.DEBUG:
            logger.info(f"Received Telegram update: {update}")

        # Handle message updates
        if 'message' in update:
            message = update['message']
            chat_id = message['chat']['id']
            text = message.get('text', '')

            # Handle /start command
            if text.startswith('/start'):
                send_webapp_button(chat_id)

            # Handle other commands here in the future
            # elif text.startswith('/help'):
            #     send_help_message(chat_id)

        # Return 200 OK to acknowledge receipt
        return HttpResponse('OK', status=200)

    except json.JSONDecodeError:
        logger.error("Invalid JSON in webhook request")
        return HttpResponse('Bad Request', status=400)

    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        return HttpResponse('Internal Server Error', status=500)


def send_webapp_button(chat_id: int):
    """
    Send a message with WebApp button to open the Mini App.

    Args:
        chat_id: Telegram chat ID
    """
    if not settings.TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN not configured")
        return

    url = f"https://api.telegram.org/bot{settings.TELEGRAM_BOT_TOKEN}/sendMessage"

    # Create inline keyboard with WebApp button
    keyboard = {
        "inline_keyboard": [[
            {
                "text": "🧾 Open Spliton",
                "web_app": {
                    "url": settings.TWA_URL
                }
            }
        ]]
    }

    payload = {
        "chat_id": chat_id,
        "text": "👋 Welcome to Spliton!\n\nSplit expenses with your friends easily. Click the button below to open the app:",
        "reply_markup": keyboard,
        "parse_mode": "HTML"
    }

    try:
        response = requests.post(url, json=payload, timeout=10)
        response.raise_for_status()

        if settings.DEBUG:
            logger.info(f"Sent WebApp button to chat {chat_id}")

    except requests.RequestException as e:
        logger.error(f"Failed to send WebApp button to chat {chat_id}: {str(e)}")


def send_help_message(chat_id: int):
    """
    Send help message (for future use).

    Args:
        chat_id: Telegram chat ID
    """
    if not settings.TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN not configured")
        return

    url = f"https://api.telegram.org/bot{settings.TELEGRAM_BOT_TOKEN}/sendMessage"

    help_text = """
🧾 <b>Spliton Bot Help</b>

<b>Commands:</b>
/start - Open the Spliton Mini App
/help - Show this help message

<b>How to use:</b>
1. Click "Open Spliton" to launch the app
2. Create or join groups
3. Add expenses and split them with friends
4. Track who owes what and settle up

The app works entirely within Telegram - no need to download anything!
    """

    payload = {
        "chat_id": chat_id,
        "text": help_text.strip(),
        "parse_mode": "HTML"
    }

    try:
        response = requests.post(url, json=payload, timeout=10)
        response.raise_for_status()

        if settings.DEBUG:
            logger.info(f"Sent help message to chat {chat_id}")

    except requests.RequestException as e:
        logger.error(f"Failed to send help message to chat {chat_id}: {str(e)}")
