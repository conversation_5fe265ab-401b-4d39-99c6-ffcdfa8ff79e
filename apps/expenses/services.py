"""
Business logic services for expense calculations.
"""
from typing import Dict, List, Tuple
from django.db.models import Sum, Q
from .models import Group, ExpenseParticipant, Settlement


def group_net_balances(group: Group) -> Dict[str, int]:
    """
    Calculate net balances for all users in a group.

    Args:
        group: Group instance

    Returns:
        Dict mapping user_id (UUID string) to net balance in currency units.
        Positive = user is owed money, Negative = user owes money
    """
    balances = {}

    # Get all group members
    member_ids = list(group.memberships.values_list('user_id', flat=True))

    # Initialize balances to 0
    for user_id in member_ids:
        balances[str(user_id)] = 0

    # Calculate expense balances (paid - share for each user)
    expense_participants = ExpenseParticipant.objects.filter(
        expense__group=group,
    ).values('user_id').annotate(
        total_paid=Sum('paid'),
        total_share=Sum('share')
    )

    for participant in expense_participants:
        user_id = str(participant['user_id'])
        total_paid = participant['total_paid'] or 0
        total_share = participant['total_share'] or 0
        balances[user_id] = total_paid - total_share

    # Apply settlements (adjust balances based on payments made)
    settlements = Settlement.objects.filter(group=group)

    for settlement in settlements:
        payer_id = str(settlement.payer_id)
        payee_id = str(settlement.payee_id)
        amount = settlement.amount

        # Payer's balance increases (they've paid off debt, reducing what they owe)
        if payer_id in balances:
            balances[payer_id] += amount

        # Payee's balance decreases (they've received payment, reducing what they're owed)
        if payee_id in balances:
            balances[payee_id] -= amount

    return balances


def simplify_transfers(balances: Dict[str, int]) -> List[Tuple[str, str, int]]:
    """
    Calculate simplified transfers to settle all debts.

    Uses a greedy algorithm to minimize the number of transactions.

    Args:
        balances: Dict mapping user_id (UUID string) to net balance in minor currency units

    Returns:
        List of tuples (from_user_id, to_user_id, amount_minor_units)
        representing suggested transfers
    """
    # Separate creditors (positive balance) and debtors (negative balance)
    creditors = []  # [(user_id, amount_owed_to_them)]
    debtors = []    # [(user_id, amount_they_owe)]

    for user_id, balance in balances.items():
        if balance > 0:
            creditors.append([user_id, balance])
        elif balance < 0:
            debtors.append([user_id, -balance])  # Store as positive amount

    # Sort creditors by amount descending, debtors by amount descending
    creditors.sort(key=lambda x: x[1], reverse=True)
    debtors.sort(key=lambda x: x[1], reverse=True)

    transfers = []

    # Greedy matching: match largest creditor with largest debtor
    while creditors and debtors:
        creditor_id, credit_amount = creditors[0]
        debtor_id, debt_amount = debtors[0]

        # Transfer the minimum of what's owed and what's due
        transfer_amount = min(credit_amount, debt_amount)

        # Record the transfer
        transfers.append((debtor_id, creditor_id, transfer_amount))

        # Update balances
        creditors[0][1] -= transfer_amount
        debtors[0][1] -= transfer_amount

        # Remove settled parties
        if creditors[0][1] == 0:
            creditors.pop(0)
        if debtors[0][1] == 0:
            debtors.pop(0)

    return transfers


def get_user_balance_in_group(user_id: str, group: Group) -> int:
    """
    Get a specific user's balance in a group.

    Args:
        user_id: User ID (UUID string)
        group: Group instance

    Returns:
        User's net balance in minor currency units
    """
    balances = group_net_balances(group)
    return balances.get(user_id, 0)


def get_group_summary(group: Group) -> Dict:
    """
    Get a comprehensive summary of group finances.

    Args:
        group: Group instance

    Returns:
        Dict containing group financial summary
    """
    # Get balances
    balances = group_net_balances(group)

    # Calculate totals
    total_expenses = group.expenses.aggregate(
        total=Sum('amount')
    )['total'] or 0

    total_settlements = group.settlements.aggregate(
        total=Sum('amount')
    )['total'] or 0

    # Count transactions
    expense_count = group.expenses.count()
    settlement_count = group.settlements.count()

    # Get suggested transfers
    suggested_transfers = simplify_transfers(balances)

    # Calculate summary stats
    total_owed = sum(max(0, balance) for balance in balances.values())
    total_owing = sum(max(0, -balance) for balance in balances.values())

    return {
        'group_id': str(group.id),
        'group_name': group.name,
        'member_count': group.memberships.count(),
        'total_expenses': total_expenses,
        'total_settlements': total_settlements,
        'expense_count': expense_count,
        'settlement_count': settlement_count,
        'total_owed': total_owed,
        'total_owing': total_owing,
        'balances': balances,
        'suggested_transfers': suggested_transfers,
        'is_settled': len(suggested_transfers) == 0,
    }
