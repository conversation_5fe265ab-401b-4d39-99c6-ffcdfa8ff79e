/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  backdrop-filter: blur(4px);
}

/* Modal Container */
.modal {
  background-color: var(--tg-theme-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--tg-theme-separator-color, #e5e5ea);
}

/* Modal Header */
.modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--tg-theme-separator-color, #e5e5ea);
  background-color: var(--tg-theme-section-bg-color, #ffffff);
}

.modal__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000000);
  margin: 0;
  line-height: 1.3;
}

.modal__close {
  background: none;
  border: none;
  font-size: 28px;
  color: var(--tg-theme-hint-color, #999999);
  cursor: pointer;
  padding: 8px;
  line-height: 1;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.modal__close:hover {
  color: var(--tg-theme-text-color, #000000);
  background-color: var(--tg-theme-secondary-bg-color, #f1f1f1);
}

/* Modal Content */
.modal__content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  background-color: var(--tg-theme-bg-color, #ffffff);
}

.form-field {
  margin-bottom: 20px;
}

.form-field:last-of-type {
  margin-bottom: 28px;
}

/* Modal Actions */
.modal__actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  padding-top: 20px;
  border-top: 1px solid var(--tg-theme-separator-color, #e5e5ea);
}

.modal__actions .tg-button {
  flex: 1;
  min-height: 44px;
  font-weight: 500;
}

/* Animation */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .modal-overlay {
    padding: 12px;
  }

  .modal {
    max-height: 95vh;
    max-width: none;
    margin: 0;
  }

  .modal__header {
    padding: 16px 20px;
  }

  .modal__content {
    padding: 20px;
  }

  .modal__actions {
    flex-direction: column;
    gap: 8px;
  }

  .modal__actions .tg-button {
    width: 100%;
    min-height: 48px;
  }
}
