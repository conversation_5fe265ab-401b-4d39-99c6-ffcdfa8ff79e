# Generated by Django 5.2.6 on 2025-09-23 01:51

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(help_text="User's email address - required for email/password authentication", max_length=254, unique=True, validators=[django.core.validators.EmailValidator()]),
        ),
        migrations.AlterField(
            model_name='user',
            name='username',
            field=models.CharField(help_text='Unique username - auto-generated if not provided', max_length=150, unique=True),
        ),
        migrations.CreateModel(
            name='AuthenticationProvider',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('provider', models.CharField(choices=[('email', 'Email/Password'), ('telegram', 'Telegram'), ('google', 'Google OAuth'), ('github', 'GitHub OAuth'), ('apple', 'Apple ID')], help_text='The authentication provider type', max_length=20)),
                ('provider_user_id', models.CharField(help_text='User ID from the external provider (e.g., Telegram user ID, Google sub claim)', max_length=255)),
                ('provider_username', models.CharField(blank=True, help_text='Username from the external provider', max_length=255, null=True)),
                ('provider_email', models.EmailField(blank=True, help_text='Email from the external provider', max_length=254, null=True)),
                ('is_primary', models.BooleanField(default=False, help_text='Whether this is the primary authentication method for the user')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this authentication provider has been verified')),
                ('linked_at', models.DateTimeField(auto_now_add=True)),
                ('last_used_at', models.DateTimeField(auto_now=True)),
                ('provider_data', models.JSONField(blank=True, default=dict, help_text='Additional provider-specific data (profile info, tokens, etc.)')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auth_providers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Authentication Provider',
                'verbose_name_plural': 'Authentication Providers',
                'db_table': 'auth_providers',
                'indexes': [models.Index(fields=['user', 'provider'], name='auth_provid_user_id_5ec796_idx'), models.Index(fields=['provider', 'provider_user_id'], name='auth_provid_provide_03dbcf_idx'), models.Index(fields=['provider_email'], name='auth_provid_provide_a54cf6_idx')],
                'unique_together': {('provider', 'provider_user_id')},
            },
        ),
    ]
