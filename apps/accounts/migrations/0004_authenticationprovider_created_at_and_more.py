# Generated by Django 5.2.6 on 2025-09-24 00:51

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_passwordresettoken'),
    ]

    operations = [
        migrations.AddField(
            model_name='authenticationprovider',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, help_text='When this provider was created'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='authenticationprovider',
            name='is_deleted',
            field=models.BooleanField(default=False, help_text='Soft delete flag - provider is marked as deleted but not removed'),
        ),
        migrations.AddField(
            model_name='authenticationprovider',
            name='is_published',
            field=models.BooleanField(default=True, help_text='Whether this provider should be visible in API responses'),
        ),
        migrations.AddField(
            model_name='authenticationprovider',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='When this provider was last updated'),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_photo',
            field=models.URLField(blank=True, help_text="URL to user's profile photo", null=True),
        ),
        migrations.AlterField(
            model_name='authenticationprovider',
            name='last_used_at',
            field=models.DateTimeField(auto_now_add=True, help_text='When this provider was last used for authentication'),
        ),
        migrations.AlterField(
            model_name='authenticationprovider',
            name='linked_at',
            field=models.DateTimeField(auto_now_add=True, help_text='When this provider was linked to the user account'),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, help_text="User's email address - required for email/password authentication", max_length=254, null=True, unique=True, validators=[django.core.validators.EmailValidator()]),
        ),
        migrations.AlterField(
            model_name='user',
            name='first_name',
            field=models.CharField(blank=True, help_text="User's first name", max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='last_name',
            field=models.CharField(blank=True, help_text="User's last name", max_length=150, null=True),
        ),
        migrations.DeleteModel(
            name='TelegramProfile',
        ),
    ]
