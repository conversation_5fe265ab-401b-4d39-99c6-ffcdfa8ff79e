/* Settlement Screen Styles */

/* Balance Card */
.settlement__balance-card {
  margin-bottom: var(--tg-spacing-16);
}

.settlement__balance {
  text-align: center;
  padding: var(--tg-spacing-8) 0;
}

.settlement__balance-label {
  font-size: var(--tg-font-size-14);
  color: var(--tg-theme-hint-color);
  margin-bottom: var(--tg-spacing-8);
}

.settlement__balance-amount {
  font-size: var(--tg-font-size-24);
  font-weight: var(--tg-font-weight-bold);
  margin-bottom: var(--tg-spacing-4);
}

.settlement__balance-amount--positive {
  color: var(--tg-color-green);
}

.settlement__balance-amount--negative {
  color: var(--tg-color-red);
}

.settlement__balance-amount--neutral {
  color: var(--tg-theme-text-color);
}

.settlement__balance-status {
  font-size: var(--tg-font-size-14);
}

/* Transfer Actions */
.settlement__transfer-actions {
  display: flex;
  align-items: center;
  gap: var(--tg-spacing-12);
}

.settlement__transfer-amount {
  font-size: var(--tg-font-size-16);
  font-weight: var(--tg-font-weight-medium);
  color: var(--tg-theme-text-color);
  font-variant-numeric: tabular-nums;
}

/* Manual Form */
.settlement__manual-form {
  margin-bottom: var(--tg-spacing-16);
}

.settlement__form-header {
  margin-bottom: var(--tg-spacing-16);
}

.settlement__form-header h3 {
  font-size: var(--tg-font-size-18);
  font-weight: var(--tg-font-weight-semibold);
  color: var(--tg-theme-text-color);
  margin: 0;
}

.settlement__user-selector {
  margin-top: var(--tg-spacing-8);
}

/* Balance Display */
.settlement__balance-display {
  font-size: var(--tg-font-size-16);
  font-weight: var(--tg-font-weight-medium);
  font-variant-numeric: tabular-nums;
}

.settlement__balance-display--positive {
  color: var(--tg-color-green);
}

.settlement__balance-display--negative {
  color: var(--tg-color-red);
}

/* Form Fields */
.form-field {
  margin-bottom: var(--tg-spacing-16);
}

.form-field:last-of-type {
  margin-bottom: var(--tg-spacing-24);
}

/* Error Message */
.error-message {
  background-color: var(--tg-color-red-light);
  color: var(--tg-color-red);
  padding: var(--tg-spacing-12);
  border-radius: var(--tg-border-radius-8);
  margin-bottom: var(--tg-spacing-16);
  font-size: var(--tg-font-size-14);
}

/* Radio Button Styling */
input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: var(--tg-theme-button-color);
}

/* Section Spacing */
.section {
  margin-bottom: var(--tg-spacing-16);
}

/* Responsive Design */
@media (max-width: 480px) {
  .settlement__transfer-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--tg-spacing-8);
  }
  
  .settlement__transfer-amount {
    font-size: var(--tg-font-size-14);
  }
}
