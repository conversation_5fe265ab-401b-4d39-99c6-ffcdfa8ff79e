import { useParams } from 'react-router-dom';

export default function ExpenseDetailScreen() {
  const { expenseId } = useParams();

  return (
    <div className="screen">
      <div className="screen__header">
        <h1 className="screen__title">Expense Details</h1>
      </div>
      <div className="screen__content">
        <div className="container">
          <p>Expense ID: {expenseId}</p>
          <p>This screen will show expense details and participants.</p>
        </div>
      </div>
    </div>
  );
}
