/* App-specific styles */
.app {
  min-height: 100vh;
  background-color: var(--tg-bg-color);
}

.screen {
  min-height: 100vh;
  background-color: var(--tg-bg-color);
  padding-bottom: env(safe-area-inset-bottom);
}

.screen__header {
  background-color: var(--tg-section-bg-color);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--tg-secondary-bg-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.screen__title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--tg-text-color);
  margin: 0;
}

.screen__content {
  flex: 1;
  padding: var(--spacing-lg);
}

.screen__content--no-padding {
  padding: 0;
}

/* Layout utilities */
.container {
  max-width: 480px;
  margin: 0 auto;
  width: 100%;
}

.section {
  background-color: var(--tg-section-bg-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.section__header {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--tg-secondary-bg-color);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--tg-section-header-text-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section__content {
  /* Content styling handled by child components */
}

/* Loading and error states */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--tg-hint-color);
}

.error {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--tg-destructive-text-color);
}

.empty-state {
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--tg-hint-color);
}

.empty-state__icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--tg-text-color);
}

.empty-state__description {
  font-size: var(--font-size-md);
  line-height: 1.5;
  margin-bottom: var(--spacing-lg);
}
