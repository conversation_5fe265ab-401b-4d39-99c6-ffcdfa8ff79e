"""
Tests for accounts models.
"""
import uuid
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from apps.accounts.models import User, AuthenticationProvider, PasswordResetToken


class UserModelTest(TestCase):
    """Test cases for User model."""

    def test_user_creation_with_uuid(self):
        """Test that User is created with UUID primary key."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Check that ID is a UUID
        self.assertIsInstance(user.id, uuid.UUID)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.is_published)
        self.assertFalse(user.is_deleted)

    def test_user_soft_deletion(self):
        """Test user soft deletion functionality."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Mark user as deleted
        user.is_deleted = True
        user.save()
        
        # User should still exist in database
        self.assertTrue(User.objects.filter(id=user.id).exists())
        self.assertTrue(user.is_deleted)

    def test_user_publication_control(self):
        """Test user publication control."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Mark user as unpublished
        user.is_published = False
        user.save()
        
        self.assertFalse(user.is_published)


    def test_user_profile_fields(self):
        """Test user profile fields."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            profile_photo='https://example.com/photo.jpg'
        )

        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.profile_photo, 'https://example.com/photo.jpg')

    def test_user_nullable_email(self):
        """Test that email can be null for auth provider users."""
        user = User.objects.create(
            username='telegram_user',
            email=None
        )
        user.set_password('testpass123')
        user.save()

        self.assertIsNone(user.email)
        self.assertEqual(user.username, 'telegram_user')


class UserProfileUpdateTest(TestCase):
    """Test cases for User profile updates."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_profile_photo_update(self):
        """Test updating profile photo."""
        self.user.profile_photo = 'https://example.com/new_photo.jpg'
        self.user.save()

        self.user.refresh_from_db()
        self.assertEqual(self.user.profile_photo, 'https://example.com/new_photo.jpg')

    def test_name_update(self):
        """Test updating first and last name."""
        self.user.first_name = 'Updated'
        self.user.last_name = 'Name'
        self.user.save()

        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')

class AuthenticationProviderModelTest(TestCase):
    """Test cases for AuthenticationProvider model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_authentication_provider_creation(self):
        """Test AuthenticationProvider creation with UUID."""
        from apps.accounts.models import AuthenticationProvider

        provider = AuthenticationProvider.objects.create(
            user=self.user,
            provider='google',
            provider_user_id='google_123456789',
            provider_username='testuser',
            provider_email='<EMAIL>',
            is_verified=True
        )

        # Check UUID primary key
        self.assertIsInstance(provider.id, uuid.UUID)
        self.assertEqual(provider.provider, 'google')
        self.assertEqual(provider.provider_user_id, 'google_123456789')
        self.assertEqual(provider.provider_username, 'testuser')
        self.assertEqual(provider.provider_email, '<EMAIL>')
        self.assertTrue(provider.is_verified)
        self.assertFalse(provider.is_primary)
        self.assertTrue(provider.is_published)
        self.assertFalse(provider.is_deleted)
        self.assertIsNotNone(provider.created_at)
        self.assertIsNotNone(provider.updated_at)

    def test_authentication_provider_unique_constraint(self):
        """Test unique constraint on provider and provider_user_id."""
        from apps.accounts.models import AuthenticationProvider
        from django.db import IntegrityError

        # Create first provider
        AuthenticationProvider.objects.create(
            user=self.user,
            provider='google',
            provider_user_id='google_123456789'
        )

        # Try to create duplicate - should fail
        with self.assertRaises(IntegrityError):
            AuthenticationProvider.objects.create(
                user=self.user,
                provider='google',
                provider_user_id='google_123456789'
            )

    def test_authentication_provider_str_representation(self):
        """Test string representation of AuthenticationProvider."""
        from apps.accounts.models import AuthenticationProvider

        provider = AuthenticationProvider.objects.create(
            user=self.user,
            provider='google',
            provider_user_id='google_123456789'
        )

        expected = f"{self.user.username} - Google OAuth"
        self.assertEqual(str(provider), expected)

    def test_authentication_provider_default_values(self):
        """Test default values for AuthenticationProvider."""
        from apps.accounts.models import AuthenticationProvider

        provider = AuthenticationProvider.objects.create(
            user=self.user,
            provider='email',
            provider_user_id='<EMAIL>'
        )

        self.assertFalse(provider.is_primary)
        self.assertFalse(provider.is_verified)
        self.assertEqual(provider.provider_data, {})
        self.assertIsNotNone(provider.linked_at)
        self.assertIsNotNone(provider.last_used_at)


class PasswordResetTokenModelTest(TestCase):
    """Test cases for PasswordResetToken model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_password_reset_token_creation(self):
        """Test PasswordResetToken creation with auto-generated token."""
        from apps.accounts.models import PasswordResetToken

        reset_token = PasswordResetToken.objects.create(user=self.user)

        # Check UUID primary key
        self.assertIsInstance(reset_token.id, uuid.UUID)
        self.assertEqual(reset_token.user, self.user)
        self.assertIsNotNone(reset_token.token)
        self.assertIsNotNone(reset_token.created_at)
        self.assertIsNotNone(reset_token.expires_at)
        self.assertIsNone(reset_token.used_at)

    def test_password_reset_token_auto_expiration(self):
        """Test that token expiration is set automatically."""
        from apps.accounts.models import PasswordResetToken
        from django.utils import timezone
        from datetime import timedelta

        before_creation = timezone.now()
        reset_token = PasswordResetToken.objects.create(user=self.user)
        after_creation = timezone.now()

        # Token should expire approximately 1 hour from creation
        expected_min = before_creation + timedelta(hours=1)
        expected_max = after_creation + timedelta(hours=1)

        self.assertGreaterEqual(reset_token.expires_at, expected_min)
        self.assertLessEqual(reset_token.expires_at, expected_max)

    def test_password_reset_token_is_valid(self):
        """Test token validity checking."""
        from apps.accounts.models import PasswordResetToken
        from django.utils import timezone
        from datetime import timedelta

        # Create valid token
        valid_token = PasswordResetToken.objects.create(user=self.user)
        self.assertTrue(valid_token.is_valid())

        # Create expired token
        expired_token = PasswordResetToken.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(hours=1)
        )
        self.assertFalse(expired_token.is_valid())

        # Create used token
        used_token = PasswordResetToken.objects.create(user=self.user)
        used_token.mark_as_used()
        self.assertFalse(used_token.is_valid())

    def test_password_reset_token_mark_as_used(self):
        """Test marking token as used."""
        from apps.accounts.models import PasswordResetToken

        reset_token = PasswordResetToken.objects.create(user=self.user)
        self.assertIsNone(reset_token.used_at)

        reset_token.mark_as_used()
        self.assertIsNotNone(reset_token.used_at)

    def test_password_reset_token_str_representation(self):
        """Test string representation of PasswordResetToken."""
        from apps.accounts.models import PasswordResetToken

        reset_token = PasswordResetToken.objects.create(user=self.user)

        expected = f"Password reset token for {self.user.email}"
        self.assertEqual(str(reset_token), expected)


