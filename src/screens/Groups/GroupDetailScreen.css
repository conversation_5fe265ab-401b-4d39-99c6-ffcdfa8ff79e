/* Group Detail Screen Styles */

.group-detail__info-card {
  margin-bottom: var(--tg-spacing-16);
}

.group-detail__info {
  display: flex;
  align-items: center;
  gap: var(--tg-spacing-12);
}

.group-detail__avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--tg-font-size-20);
  font-weight: var(--tg-font-weight-semibold);
  text-transform: uppercase;
}

.group-detail__meta {
  flex: 1;
}

.group-detail__name {
  font-size: var(--tg-font-size-18);
  font-weight: var(--tg-font-weight-semibold);
  color: var(--tg-theme-text-color);
  margin-bottom: var(--tg-spacing-4);
}

.group-detail__description {
  font-size: var(--tg-font-size-14);
  margin-bottom: var(--tg-spacing-4);
}

.group-detail__members {
  font-size: var(--tg-font-size-14);
}

/* Balance Card */
.group-detail__balance-card {
  margin-bottom: var(--tg-spacing-16);
}

.group-detail__balance {
  text-align: center;
  padding: var(--tg-spacing-8) 0;
}

.group-detail__balance-label {
  font-size: var(--tg-font-size-14);
  color: var(--tg-theme-hint-color);
  margin-bottom: var(--tg-spacing-8);
}

.group-detail__balance-amount {
  font-size: var(--tg-font-size-24);
  font-weight: var(--tg-font-weight-bold);
  margin-bottom: var(--tg-spacing-4);
}

.group-detail__balance-amount--positive {
  color: var(--tg-color-green);
}

.group-detail__balance-amount--negative {
  color: var(--tg-color-red);
}

.group-detail__balance-amount--neutral {
  color: var(--tg-theme-text-color);
}

.group-detail__balance-status {
  font-size: var(--tg-font-size-14);
}

/* Action Buttons */
.group-detail__actions {
  display: flex;
  flex-direction: column;
  gap: var(--tg-spacing-12);
  margin-bottom: var(--tg-spacing-24);
}

/* Expense List */
.expense-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--tg-font-size-16);
}

.expense-amount {
  font-size: var(--tg-font-size-16);
  font-weight: var(--tg-font-weight-medium);
  color: var(--tg-theme-text-color);
  font-variant-numeric: tabular-nums;
}

/* Responsive Design */
@media (min-width: 480px) {
  .group-detail__actions {
    flex-direction: row;
  }
  
  .group-detail__actions .tg-button {
    flex: 1;
  }
}
