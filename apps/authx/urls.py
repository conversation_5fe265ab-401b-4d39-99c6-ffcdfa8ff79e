"""
URL patterns for authentication endpoints.
"""
from django.urls import path
from . import views

app_name = 'authx'

urlpatterns = [
    # Email/Password Authentication
    path('register/', views.register, name='register'),
    path('login/', views.login, name='login'),

    # Password Management
    path('password/reset/request/', views.password_reset_request, name='password_reset_request'),
    path('password/reset/confirm/', views.password_reset_confirm, name='password_reset_confirm'),
    path('password/change/', views.change_password, name='change_password'),

    # Account Linking
    path('link/', views.link_account, name='link_account'),
    path('unlink/', views.unlink_account, name='unlink_account'),

    # User Profile
    path('profile/', views.profile, name='profile'),
    path('profile/update/', views.update_profile, name='update_profile'),

    # Telegram Authentication
    path('telegram_webapp/verify/', views.telegram_webapp_verify, name='telegram_webapp_verify'),
    path('telegram_webapp/link/', views.link_telegram, name='link_telegram'),
]
