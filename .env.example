# Django Configuration
DJANGO_DEBUG=True
DJANGO_SECRET_KEY=change-me-to-a-secure-random-string
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
# For PostgreSQL (production and development):
DATABASE_URL=postgresql://spliton_user:spliton@localhost:5432/spliton

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=123456:ABC-DEF-your-bot-token-here

# CORS and CSRF Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:5173
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:5173

# Telegram Web App URL (for bot buttons)
TWA_URL=https://your-domain.com/webapp

# JWT Configuration (optional overrides)
# JWT_ACCESS_TOKEN_LIFETIME_MINUTES=20
# JWT_REFRESH_TOKEN_LIFETIME_DAYS=14
