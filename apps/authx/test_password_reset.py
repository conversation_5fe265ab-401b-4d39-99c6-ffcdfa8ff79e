"""
Tests for password reset functionality.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.accounts.models import PasswordResetToken
from django.utils import timezone
from datetime import timedelta
import json

User = get_user_model()


class PasswordResetTest(APITestCase):
    """Test password reset functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.register_url = reverse('authx:register')
        self.login_url = reverse('authx:login')
        self.reset_request_url = reverse('authx:password_reset_request')
        self.reset_confirm_url = reverse('authx:password_reset_confirm')
        self.change_password_url = reverse('authx:change_password')
        
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        # Create user
        self.client.post(self.register_url, self.user_data, format='json')
        self.user = User.objects.get(email='<EMAIL>')
    
    def test_password_reset_request_success(self):
        """Test successful password reset request."""
        request_data = {
            'email': '<EMAIL>'
        }
        
        response = self.client.post(self.reset_request_url, request_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Check that token was created
        token = PasswordResetToken.objects.filter(user=self.user, used_at__isnull=True).first()
        self.assertIsNotNone(token)
        self.assertTrue(token.is_valid())
    
    def test_password_reset_request_nonexistent_email(self):
        """Test password reset request for non-existent email."""
        request_data = {
            'email': '<EMAIL>'
        }
        
        response = self.client.post(self.reset_request_url, request_data, format='json')
        
        # Should still return success for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # But no token should be created
        self.assertFalse(
            PasswordResetToken.objects.filter(
                user__email='<EMAIL>'
            ).exists()
        )
    
    def test_password_reset_request_invalidates_old_tokens(self):
        """Test that new reset request invalidates old tokens."""
        request_data = {
            'email': '<EMAIL>'
        }
        
        # Create first token
        response1 = self.client.post(self.reset_request_url, request_data, format='json')
        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        
        token1 = PasswordResetToken.objects.filter(user=self.user, used_at__isnull=True).first()
        self.assertIsNotNone(token1)
        
        # Create second token
        response2 = self.client.post(self.reset_request_url, request_data, format='json')
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        
        # First token should be marked as used
        token1.refresh_from_db()
        self.assertIsNotNone(token1.used_at)
        
        # New token should exist
        token2 = PasswordResetToken.objects.filter(user=self.user, used_at__isnull=True).first()
        self.assertIsNotNone(token2)
        self.assertNotEqual(token1.id, token2.id)
    
    def test_password_reset_confirm_success(self):
        """Test successful password reset confirmation."""
        # Create reset token
        request_data = {
            'email': '<EMAIL>'
        }
        self.client.post(self.reset_request_url, request_data, format='json')
        
        token = PasswordResetToken.objects.filter(user=self.user, used_at__isnull=True).first()
        
        # Confirm reset
        confirm_data = {
            'email': '<EMAIL>',
            'token': token.token,
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.reset_confirm_url, confirm_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Check that token was marked as used
        token.refresh_from_db()
        self.assertIsNotNone(token.used_at)
        
        # Check that password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewPassword123!'))
        self.assertFalse(self.user.check_password('TestPassword123!'))
    
    def test_password_reset_confirm_invalid_token(self):
        """Test password reset confirmation with invalid token."""
        confirm_data = {
            'email': '<EMAIL>',
            'token': 'invalid_token',
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.reset_confirm_url, confirm_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_password_reset_confirm_expired_token(self):
        """Test password reset confirmation with expired token."""
        # Create token and manually expire it
        token = PasswordResetToken.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(hours=1)  # Expired 1 hour ago
        )
        
        confirm_data = {
            'email': '<EMAIL>',
            'token': token.token,
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.reset_confirm_url, confirm_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('expired', response.data['error'])
    
    def test_password_reset_confirm_used_token(self):
        """Test password reset confirmation with already used token."""
        # Create and use token
        token = PasswordResetToken.objects.create(user=self.user)
        token.mark_as_used()
        
        confirm_data = {
            'email': '<EMAIL>',
            'token': token.token,
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.reset_confirm_url, confirm_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('used', response.data['error'])
    
    def test_password_reset_confirm_weak_password(self):
        """Test password reset confirmation with weak password."""
        # Create reset token
        request_data = {
            'email': '<EMAIL>'
        }
        self.client.post(self.reset_request_url, request_data, format='json')
        
        token = PasswordResetToken.objects.filter(user=self.user, used_at__isnull=True).first()
        
        # Confirm reset with weak password
        confirm_data = {
            'email': '<EMAIL>',
            'token': token.token,
            'new_password': '123'
        }
        
        response = self.client.post(self.reset_confirm_url, confirm_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('new_password', response.data['details'])


class ChangePasswordTest(APITestCase):
    """Test change password functionality for authenticated users."""
    
    def setUp(self):
        """Set up test data."""
        self.register_url = reverse('authx:register')
        self.login_url = reverse('authx:login')
        self.change_password_url = reverse('authx:change_password')
        
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        # Create and authenticate user
        self.client.post(self.register_url, self.user_data, format='json')
        login_response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }, format='json')
        
        self.access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        self.user = User.objects.get(email='<EMAIL>')
    
    def test_change_password_success(self):
        """Test successful password change."""
        change_data = {
            'current_password': 'TestPassword123!',
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.change_password_url, change_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Check that password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewPassword123!'))
        self.assertFalse(self.user.check_password('TestPassword123!'))
    
    def test_change_password_wrong_current_password(self):
        """Test password change with wrong current password."""
        change_data = {
            'current_password': 'WrongPassword123!',
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.change_password_url, change_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('current_password', response.data['details'])
    
    def test_change_password_unauthenticated(self):
        """Test password change without authentication."""
        self.client.credentials()  # Remove authentication
        
        change_data = {
            'current_password': 'TestPassword123!',
            'new_password': 'NewPassword123!'
        }
        
        response = self.client.post(self.change_password_url, change_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    

