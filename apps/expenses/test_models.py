"""
Tests for expenses models.
"""
import uuid
from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from apps.accounts.models import User
from apps.expenses.models import Group, Membership, Expense, ExpenseParticipant, Settlement, to_major_units


class HelperFunctionTest(TestCase):
    """Test cases for helper functions."""

    def test_to_major_units_conversion(self):
        """Test conversion from minor to major units."""
        self.assertEqual(to_major_units(100), 1.0)
        self.assertEqual(to_major_units(2550), 25.5)
        self.assertEqual(to_major_units(0), 0.0)
        self.assertEqual(to_major_units(None), 0)


class GroupModelTest(TestCase):
    """Test cases for Group model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_group_creation_with_uuid(self):
        """Test Group creation with UUID primary key."""
        group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )
        
        self.assertIsInstance(group.id, uuid.UUID)
        self.assertEqual(group.name, 'Test Group')
        self.assertEqual(group.created_by, self.user)
        self.assertTrue(group.is_published)
        self.assertFalse(group.is_deleted)

    def test_group_str_representation(self):
        """Test Group string representation."""
        group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )
        self.assertEqual(str(group), 'Test Group')

    def test_group_soft_deletion(self):
        """Test Group soft deletion preserves expense history."""
        group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )
        
        # Mark as deleted
        group.is_deleted = True
        group.save()
        
        # Should still exist in database
        self.assertTrue(Group.objects.filter(id=group.id).exists())
        self.assertTrue(group.is_deleted)


class MembershipModelTest(TestCase):
    """Test cases for Membership model."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )

    def test_membership_creation(self):
        """Test Membership creation."""
        membership = Membership.objects.create(
            user=self.user2,
            group=self.group,
            role='member'
        )
        
        self.assertIsInstance(membership.id, uuid.UUID)
        self.assertEqual(membership.user, self.user2)
        self.assertEqual(membership.group, self.group)
        self.assertEqual(membership.role, 'member')
        self.assertTrue(membership.is_published)

    def test_membership_unique_constraint(self):
        """Test that user can't be member of same group twice."""
        Membership.objects.create(
            user=self.user2,
            group=self.group,
            role='member'
        )
        
        # Try to create duplicate membership
        with self.assertRaises(IntegrityError):
            Membership.objects.create(
                user=self.user2,
                group=self.group,
                role='admin'
            )

    def test_membership_str_representation(self):
        """Test Membership string representation."""
        membership = Membership.objects.create(
            user=self.user2,
            group=self.group,
            role='admin'
        )
        
        expected = f"{self.user2.username} in {self.group.name} (admin)"
        self.assertEqual(str(membership), expected)


class ExpenseModelTest(TestCase):
    """Test cases for Expense model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user
        )

    def test_expense_creation(self):
        """Test Expense creation with currency-neutral fields."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user,
            description='Test Expense',
            amount=5000,  # 50.00 in major units
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        self.assertIsInstance(expense.id, uuid.UUID)
        self.assertEqual(expense.amount, 5000)
        self.assertEqual(expense.amount_major_units, 50.0)
        self.assertEqual(expense.currency, 'USD')
        self.assertTrue(expense.is_published)

    def test_expense_str_representation(self):
        """Test Expense string representation."""
        expense = Expense.objects.create(
            group=self.group,
            created_by=self.user,
            description='Test Expense',
            amount=5000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )
        
        expected = "Test Expense - 50.00 USD"
        self.assertEqual(str(expense), expected)

    def test_expense_amount_validation(self):
        """Test that expense amount must be positive."""
        with self.assertRaises(ValidationError):
            expense = Expense(
                group=self.group,
                created_by=self.user,
                description='Invalid Expense',
                amount=0,  # Invalid: must be >= 1
                currency='USD',
                occurred_at='2025-09-22T12:00:00Z'
            )
            expense.full_clean()


class ExpenseParticipantModelTest(TestCase):
    """Test cases for ExpenseParticipant model."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )
        self.expense = Expense.objects.create(
            group=self.group,
            created_by=self.user1,
            description='Test Expense',
            amount=6000,
            currency='USD',
            occurred_at='2025-09-22T12:00:00Z'
        )

    def test_expense_participant_creation(self):
        """Test ExpenseParticipant creation with new field names."""
        participant = ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user1,
            share=3000,  # User owes 30.00
            paid=6000    # User paid 60.00
        )
        
        self.assertIsInstance(participant.id, uuid.UUID)
        self.assertEqual(participant.share, 3000)
        self.assertEqual(participant.paid, 6000)
        self.assertEqual(participant.share_major_units, 30.0)
        self.assertEqual(participant.paid_major_units, 60.0)
        self.assertEqual(participant.net_minor_units, 3000)  # 60.00 - 30.00
        self.assertEqual(participant.net_major_units, 30.0)
        self.assertTrue(participant.is_published)

    def test_expense_participant_unique_constraint(self):
        """Test that user can't participate in same expense twice."""
        ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user1,
            share=3000,
            paid=6000
        )
        
        # Try to create duplicate participation
        with self.assertRaises(IntegrityError):
            ExpenseParticipant.objects.create(
                expense=self.expense,
                user=self.user1,
                share=1000,
                paid=0
            )

    def test_expense_participant_str_representation(self):
        """Test ExpenseParticipant string representation."""
        participant = ExpenseParticipant.objects.create(
            expense=self.expense,
            user=self.user1,
            share=3000,
            paid=6000
        )
        
        expected = f"{self.user1.username} - {self.expense.description}"
        self.assertEqual(str(participant), expected)


class SettlementModelTest(TestCase):
    """Test cases for Settlement model."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.group = Group.objects.create(
            name='Test Group',
            created_by=self.user1
        )

    def test_settlement_creation(self):
        """Test Settlement creation with new field names."""
        settlement = Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount=3000,  # 30.00 in major units
            currency='USD',
            settled_at='2025-09-22T12:00:00Z',
            note='Cash payment'
        )

        self.assertIsInstance(settlement.id, uuid.UUID)
        self.assertEqual(settlement.amount, 3000)
        self.assertEqual(settlement.amount_major_units, 30.0)
        self.assertEqual(settlement.currency, 'USD')
        self.assertEqual(settlement.note, 'Cash payment')
        self.assertTrue(settlement.is_published)

    def test_settlement_str_representation(self):
        """Test Settlement string representation."""
        settlement = Settlement.objects.create(
            group=self.group,
            payer=self.user2,
            payee=self.user1,
            amount=3000,
            currency='USD',
            settled_at='2025-09-22T12:00:00Z'
        )

        expected = f"{self.user2.username} → {self.user1.username}: 30.00 USD"
        self.assertEqual(str(settlement), expected)

    def test_settlement_validation_same_user(self):
        """Test that payer and payee cannot be the same user."""
        settlement = Settlement(
            group=self.group,
            payer=self.user1,
            payee=self.user1,  # Same as payer
            amount=3000,
            currency='USD',
            settled_at='2025-09-22T12:00:00Z'
        )

        with self.assertRaises(ValidationError):
            settlement.clean()

    def test_settlement_amount_validation(self):
        """Test that settlement amount must be positive."""
        with self.assertRaises(ValidationError):
            settlement = Settlement(
                group=self.group,
                payer=self.user2,
                payee=self.user1,
                amount=0,  # Invalid: must be >= 1
                currency='USD',
                settled_at='2025-09-22T12:00:00Z'
            )
            settlement.full_clean()
